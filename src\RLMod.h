#pragma once
#include "pch.h"

class RLMod
{
private:
    bool m_bInitialized;
    HMODULE m_hGameModule;
    uintptr_t m_gameBase;
    
    // Hook related
    std::map<std::string, void*> m_originalFunctions;
    std::map<std::string, void*> m_hookedFunctions;
    
    // Game state
    bool m_bGameLoaded;
    void* m_pGameInstance;
    void* m_pLocalPlayer;
    
public:
    RLMod();
    ~RLMod();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // Game access
    bool IsGameLoaded() const { return m_bGameLoaded; }
    void* GetGameInstance() const { return m_pGameInstance; }
    void* GetLocalPlayer() const { return m_pLocalPlayer; }
    uintptr_t GetGameBase() const { return m_gameBase; }
    
    // Hook management
    bool InstallHook(const std::string& name, void* target, void* detour);
    bool RemoveHook(const std::string& name);
    void* GetOriginalFunction(const std::string& name);
    
    // Memory utilities
    template<typename T>
    T ReadMemory(uintptr_t address) {
        T value = {};
        Memory::ReadMemory(reinterpret_cast<void*>(address), &value, sizeof(T));
        return value;
    }
    
    template<typename T>
    bool WriteMemory(uintptr_t address, const T& value) {
        return Memory::WriteMemory(reinterpret_cast<void*>(address), &value, sizeof(T));
    }
    
    // Pattern scanning
    uintptr_t FindPattern(const char* pattern, const char* mask);
    
private:
    bool FindGameObjects();
    void UpdateGameState();
};
