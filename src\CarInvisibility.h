#pragma once
#include "pch.h"

class CarInvisibility
{
private:
    bool m_bInitialized;
    bool m_bCarInvisible;
    
    // Hook addresses
    void* m_pRenderCarFunction;
    void* m_pDrawMeshFunction;
    void* m_pSetVisibilityFunction;
    
    // Car object pointers
    void* m_pLocalCar;
    void* m_pCarMesh;
    std::vector<void*> m_carComponents;
    
    // Original function pointers
    typedef void(__fastcall* RenderCarFunc)(void* carObject, void* context);
    typedef void(__fastcall* DrawMeshFunc)(void* meshObject, void* renderContext);
    typedef void(__fastcall* SetVisibilityFunc)(void* object, bool visible);
    
    RenderCarFunc m_originalRenderCar;
    DrawMeshFunc m_originalDrawMesh;
    SetVisibilityFunc m_originalSetVisibility;
    
public:
    CarInvisibility();
    ~CarInvisibility();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // Main functionality
    void ToggleCarInvisibility();
    void SetCarInvisible(bool invisible);
    bool IsCarInvisible() const { return m_bCarInvisible; }
    
    // Hook functions
    static void __fastcall HookedRenderCar(void* carObject, void* context);
    static void __fastcall HookedDrawMesh(void* meshObject, void* renderContext);
    static void __fastcall HookedSetVisibility(void* object, bool visible);
    
private:
    bool FindCarRenderFunctions();
    bool FindLocalCar();
    void UpdateCarComponents();
    bool IsLocalCarComponent(void* component);
    
    // Visibility methods
    void SetMeshVisibility(void* mesh, bool visible);
    void SetComponentVisibility(void* component, bool visible);
    void ApplyInvisibilityToAllComponents();
};
