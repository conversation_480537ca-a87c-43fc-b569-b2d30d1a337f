#include "pch.h"
#include "UI.h"
#include "CarInvisibility.h"
#include "TitleSystem.h"

// Global instances
extern UI* g_pUI;
extern CarInvisibility* g_pCarInvis;
extern TitleSystem* g_pTitleSystem;

UI::UI() : m_bInitialized(false), m_bShow<PERSON>(false), m_bShowDemo(false),
    m_pDevice(nullptr), m_pContext(nullptr), m_pSwapChain(nullptr), m_pRenderTargetView(nullptr),
    m_hWnd(nullptr), m_originalWndProc(nullptr),
    m_bCarInvisibilityEnabled(false), m_selectedTitleIndex(0), m_bShowTitleSelector(false), m_bShowSettings(false),
    m_bF1Pressed(false), m_bF2Pressed(false), m_bInsertPressed(false)
{
    memset(m_customTitleBuffer, 0, sizeof(m_customTitleBuffer));
}

UI::~UI()
{
    Shutdown();
}

bool UI::Initialize()
{
    if (m_bInitialized) return true;
    
    // Find game window
    if (!FindGameWindow()) {
        return false;
    }
    
    // Initialize DirectX
    if (!InitializeDirectX()) {
        return false;
    }
    
    // Initialize ImGui
    if (!InitializeImGui()) {
        return false;
    }
    
    // Hook window procedure
    if (!HookWindowProc()) {
        return false;
    }
    
    m_bInitialized = true;
    return true;
}

void UI::Update()
{
    if (!m_bInitialized) return;
    
    HandleInput();
    
    if (m_bShowUI) {
        Render();
    }
}

void UI::Shutdown()
{
    if (!m_bInitialized) return;
    
    UnhookWindowProc();
    CleanupImGui();
    CleanupDirectX();
    
    m_bInitialized = false;
}

void UI::Render()
{
    if (!m_pDevice || !m_pContext) return;
    
    // Start ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();
    
    // Render UI windows
    RenderMainWindow();
    
    if (m_bShowTitleSelector) {
        RenderTitleSelector();
    }
    
    if (m_bShowSettings) {
        RenderSettings();
    }
    
    // Render ImGui
    ImGui::Render();
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
}

void UI::RenderMainWindow()
{
    ImGui::SetNextWindowSize(ImVec2(400, 300), ImGuiCond_FirstUseEver);
    ImGui::Begin("Rocket League Mod", &m_bShowUI, ImGuiWindowFlags_NoCollapse);
    
    ImGui::Text("Rocket League Mod v1.0");
    ImGui::Separator();
    
    // Car invisibility controls
    DrawCarInvisibilityControls();
    
    ImGui::Separator();
    
    // Title controls
    DrawTitleControls();
    
    ImGui::Separator();
    
    // Hotkey info
    DrawHotkeyInfo();
    
    ImGui::Separator();
    
    // Status info
    DrawStatusInfo();
    
    ImGui::End();
}

void UI::RenderTitleSelector()
{
    ImGui::SetNextWindowSize(ImVec2(350, 400), ImGuiCond_FirstUseEver);
    ImGui::Begin("Title Selector", &m_bShowTitleSelector);
    
    if (g_pTitleSystem) {
        const auto& titles = g_pTitleSystem->GetAvailableTitles();
        
        ImGui::Text("Available Titles:");
        ImGui::Separator();
        
        for (int i = 0; i < static_cast<int>(titles.size()); i++) {
            bool isSelected = (i == g_pTitleSystem->GetCurrentTitleIndex());
            
            if (ImGui::Selectable(titles[i].displayText.c_str(), isSelected)) {
                g_pTitleSystem->SetTitle(i);
                m_selectedTitleIndex = i;
            }
            
            if (titles[i].isCustom) {
                ImGui::SameLine();
                ImGui::TextColored(ImVec4(0.5f, 1.0f, 0.5f, 1.0f), "(Custom)");
            }
        }
        
        ImGui::Separator();
        
        // Custom title input
        ImGui::Text("Add Custom Title:");
        ImGui::InputText("##CustomTitle", m_customTitleBuffer, sizeof(m_customTitleBuffer));
        ImGui::SameLine();
        
        if (ImGui::Button("Add")) {
            if (strlen(m_customTitleBuffer) > 0) {
                g_pTitleSystem->SetCustomTitle(m_customTitleBuffer);
                memset(m_customTitleBuffer, 0, sizeof(m_customTitleBuffer));
            }
        }
    }
    
    ImGui::End();
}

void UI::RenderSettings()
{
    ImGui::SetNextWindowSize(ImVec2(300, 200), ImGuiCond_FirstUseEver);
    ImGui::Begin("Settings", &m_bShowSettings);
    
    ImGui::Text("Hotkeys:");
    ImGui::Text("INSERT - Toggle UI");
    ImGui::Text("F1 - Toggle Car Invisibility");
    ImGui::Text("F2 - Cycle Titles");
    
    ImGui::Separator();
    
    ImGui::Text("About:");
    ImGui::Text("Rocket League Mod v1.0");
    ImGui::Text("Educational purposes only");
    
    ImGui::End();
}

void UI::HandleInput()
{
    // Check for hotkeys
    bool f1Current = GetAsyncKeyState(VK_F1) & 0x8000;
    bool f2Current = GetAsyncKeyState(VK_F2) & 0x8000;
    bool insertCurrent = GetAsyncKeyState(VK_INSERT) & 0x8000;
    
    // F1 - Toggle car invisibility
    if (f1Current && !m_bF1Pressed) {
        if (g_pCarInvis) {
            g_pCarInvis->ToggleCarInvisibility();
            m_bCarInvisibilityEnabled = g_pCarInvis->IsCarInvisible();
        }
    }
    m_bF1Pressed = f1Current;
    
    // F2 - Cycle titles
    if (f2Current && !m_bF2Pressed) {
        if (g_pTitleSystem) {
            g_pTitleSystem->CycleTitle();
            m_selectedTitleIndex = g_pTitleSystem->GetCurrentTitleIndex();
        }
    }
    m_bF2Pressed = f2Current;
    
    // INSERT - Toggle UI
    if (insertCurrent && !m_bInsertPressed) {
        ToggleUI();
    }
    m_bInsertPressed = insertCurrent;
}

void UI::DrawCarInvisibilityControls()
{
    ImGui::Text("Car Invisibility:");
    
    if (g_pCarInvis) {
        m_bCarInvisibilityEnabled = g_pCarInvis->IsCarInvisible();
        
        if (ImGui::Checkbox("Make Car Invisible", &m_bCarInvisibilityEnabled)) {
            g_pCarInvis->SetCarInvisible(m_bCarInvisibilityEnabled);
        }
        
        ImGui::SameLine();
        ImGui::TextColored(
            m_bCarInvisibilityEnabled ? ImVec4(0.5f, 1.0f, 0.5f, 1.0f) : ImVec4(1.0f, 0.5f, 0.5f, 1.0f),
            m_bCarInvisibilityEnabled ? "ENABLED" : "DISABLED"
        );
    } else {
        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Car Invisibility System Not Available");
    }
}

void UI::DrawTitleControls()
{
    ImGui::Text("Title System:");
    
    if (g_pTitleSystem) {
        std::string currentTitle = g_pTitleSystem->GetCurrentTitle();
        ImGui::Text("Current Title: %s", currentTitle.c_str());
        
        if (ImGui::Button("Select Title")) {
            m_bShowTitleSelector = true;
        }
        
        ImGui::SameLine();
        
        if (ImGui::Button("Cycle Title (F2)")) {
            g_pTitleSystem->CycleTitle();
            m_selectedTitleIndex = g_pTitleSystem->GetCurrentTitleIndex();
        }
    } else {
        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Title System Not Available");
    }
}

void UI::DrawHotkeyInfo()
{
    ImGui::Text("Hotkeys:");
    ImGui::BulletText("INSERT: Toggle this UI");
    ImGui::BulletText("F1: Toggle car invisibility");
    ImGui::BulletText("F2: Cycle through titles");
    
    if (ImGui::Button("Settings")) {
        m_bShowSettings = true;
    }
}

void UI::DrawStatusInfo()
{
    ImGui::Text("Status:");

    // Car invisibility status
    if (g_pCarInvis) {
        ImGui::BulletText("Car Invisibility: %s",
            g_pCarInvis->IsCarInvisible() ? "ACTIVE" : "INACTIVE");
    }

    // Title system status
    if (g_pTitleSystem) {
        ImGui::BulletText("Title System: ACTIVE");
        ImGui::BulletText("Current Title: %s", g_pTitleSystem->GetCurrentTitle().c_str());
    }
}

bool UI::FindGameWindow()
{
    m_hWnd = FindWindowA(nullptr, "Rocket League");
    if (!m_hWnd) {
        m_hWnd = FindWindowA(nullptr, "Rocket League (64-bit, DX11, Cooked)");
    }
    if (!m_hWnd) {
        m_hWnd = FindWindowA(nullptr, "Rocket League (32-bit, DX11, Cooked)");
    }

    return (m_hWnd != nullptr);
}

bool UI::InitializeDirectX()
{
    // Get DirectX device from the game
    // This is a simplified approach - in practice you'd need to hook the game's D3D device

    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 1;
    swapChainDesc.BufferDesc.Width = 0;
    swapChainDesc.BufferDesc.Height = 0;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferDesc.RefreshRate.Numerator = 60;
    swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = m_hWnd;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.SampleDesc.Quality = 0;
    swapChainDesc.Windowed = TRUE;

    D3D_FEATURE_LEVEL featureLevel;
    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
        nullptr, 0, D3D11_SDK_VERSION,
        &swapChainDesc, &m_pSwapChain,
        &m_pDevice, &featureLevel, &m_pContext
    );

    if (FAILED(hr)) {
        return false;
    }

    // Create render target view
    ID3D11Texture2D* pBackBuffer;
    hr = m_pSwapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (void**)&pBackBuffer);
    if (FAILED(hr)) {
        return false;
    }

    hr = m_pDevice->CreateRenderTargetView(pBackBuffer, nullptr, &m_pRenderTargetView);
    pBackBuffer->Release();

    return SUCCEEDED(hr);
}

bool UI::InitializeImGui()
{
    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup style
    ImGui::StyleColorsDark();

    // Setup platform/renderer bindings
    if (!ImGui_ImplWin32_Init(m_hWnd)) {
        return false;
    }

    if (!ImGui_ImplDX11_Init(m_pDevice, m_pContext)) {
        return false;
    }

    return true;
}

void UI::CleanupDirectX()
{
    SAFE_RELEASE(m_pRenderTargetView);
    SAFE_RELEASE(m_pSwapChain);
    SAFE_RELEASE(m_pContext);
    SAFE_RELEASE(m_pDevice);
}

void UI::CleanupImGui()
{
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
}

bool UI::HookWindowProc()
{
    if (!m_hWnd) return false;

    m_originalWndProc = (WNDPROC)SetWindowLongPtr(m_hWnd, GWLP_WNDPROC, (LONG_PTR)WndProc);
    return (m_originalWndProc != nullptr);
}

void UI::UnhookWindowProc()
{
    if (m_hWnd && m_originalWndProc) {
        SetWindowLongPtr(m_hWnd, GWLP_WNDPROC, (LONG_PTR)m_originalWndProc);
        m_originalWndProc = nullptr;
    }
}

LRESULT CALLBACK UI::WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (g_pUI && g_pUI->m_bShowUI) {
        ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam);

        // Block input to game when UI is visible
        if (ImGui::GetIO().WantCaptureMouse || ImGui::GetIO().WantCaptureKeyboard) {
            return true;
        }
    }

    // Call original window procedure
    if (g_pUI && g_pUI->m_originalWndProc) {
        return CallWindowProc(g_pUI->m_originalWndProc, hWnd, msg, wParam, lParam);
    }

    return DefWindowProc(hWnd, msg, wParam, lParam);
}
