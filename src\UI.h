#pragma once
#include "pch.h"

class UI
{
private:
    bool m_bInitialized;
    bool m_bShowUI;
    bool m_bShowDemo;
    
    // DirectX resources
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGISwapChain* m_pSwapChain;
    ID3D11RenderTargetView* m_pRenderTargetView;
    
    // Window handle
    HWND m_hWnd;
    WNDPROC m_originalWndProc;
    
    // UI state
    bool m_bCarInvisibilityEnabled;
    int m_selectedTitleIndex;
    char m_customTitleBuffer[256];
    bool m_bShowTitleSelector;
    bool m_bShowSettings;
    
    // Hotkeys
    bool m_bF1Pressed;
    bool m_bF2Pressed;
    bool m_bInsertPressed;
    
public:
    UI();
    ~UI();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // UI control
    void ToggleUI() { m_bShowUI = !m_bShowUI; }
    bool IsUIVisible() const { return m_bShowUI; }
    
    // Rendering
    void Render();
    void RenderMainWindow();
    void RenderTitleSelector();
    void RenderSettings();
    
    // Input handling
    void HandleInput();
    static LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
private:
    bool InitializeDirectX();
    bool InitializeImGui();
    void CleanupDirectX();
    void CleanupImGui();
    
    bool FindGameWindow();
    bool HookWindowProc();
    void UnhookWindowProc();
    
    // UI helpers
    void DrawCarInvisibilityControls();
    void DrawTitleControls();
    void DrawHotkeyInfo();
    void DrawStatusInfo();
};
