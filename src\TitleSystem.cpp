#include "pch.h"
#include "TitleSystem.h"
#include "RLMod.h"

// Global instance for hook access
extern TitleSystem* g_pTitleSystem;
extern RLMod* g_pMod;

TitleSystem::TitleSystem() : m_bInitialized(false), m_currentTitleIndex(0),
    m_pGetPlayerTitleFunction(nullptr), m_pSetPlayerTitleFunction(nullptr),
    m_pSendTitleDataFunction(nullptr), m_pReceiveTitleDataFunction(nullptr),
    m_originalGetPlayerTitle(nullptr), m_originalSetPlayerTitle(nullptr),
    m_originalSendTitleData(nullptr), m_originalReceiveTitleData(nullptr),
    m_pLocalPlayer(nullptr)
{
}

TitleSystem::~TitleSystem()
{
    Shutdown();
}

bool TitleSystem::Initialize()
{
    if (m_bInitialized) return true;
    
    if (!g_pMod || !g_pMod->IsGameLoaded()) {
        return false;
    }
    
    // Initialize title lists
    InitializeDefaultTitles();
    LoadCustomTitles();
    
    // Find title-related functions
    if (!FindTitleFunctions()) {
        return false;
    }
    
    // Install hooks
    if (m_pGetPlayerTitleFunction) {
        g_pMod->InstallHook("GetPlayerTitle", m_pGetPlayerTitleFunction, &HookedGetPlayerTitle);
        m_originalGetPlayerTitle = reinterpret_cast<GetPlayerTitleFunc>(g_pMod->GetOriginalFunction("GetPlayerTitle"));
    }
    
    if (m_pSetPlayerTitleFunction) {
        g_pMod->InstallHook("SetPlayerTitle", m_pSetPlayerTitleFunction, &HookedSetPlayerTitle);
        m_originalSetPlayerTitle = reinterpret_cast<SetPlayerTitleFunc>(g_pMod->GetOriginalFunction("SetPlayerTitle"));
    }
    
    if (m_pSendTitleDataFunction) {
        g_pMod->InstallHook("SendTitleData", m_pSendTitleDataFunction, &HookedSendTitleData);
        m_originalSendTitleData = reinterpret_cast<SendTitleDataFunc>(g_pMod->GetOriginalFunction("SendTitleData"));
    }
    
    m_bInitialized = true;
    return true;
}

void TitleSystem::Update()
{
    if (!m_bInitialized) return;
    
    // Update local player reference
    m_pLocalPlayer = g_pMod->GetLocalPlayer();
}

void TitleSystem::Shutdown()
{
    if (!m_bInitialized) return;
    
    // Remove hooks
    if (g_pMod) {
        g_pMod->RemoveHook("GetPlayerTitle");
        g_pMod->RemoveHook("SetPlayerTitle");
        g_pMod->RemoveHook("SendTitleData");
    }
    
    m_bInitialized = false;
}

void TitleSystem::CycleTitle()
{
    m_currentTitleIndex = (m_currentTitleIndex + 1) % m_availableTitles.size();
}

void TitleSystem::SetTitle(int index)
{
    if (index >= 0 && index < static_cast<int>(m_availableTitles.size())) {
        m_currentTitleIndex = index;
    }
}

void TitleSystem::SetCustomTitle(const std::string& title)
{
    m_customTitle = title;
    
    // Add to available titles if not already present
    bool found = false;
    for (const auto& titleInfo : m_availableTitles) {
        if (titleInfo.displayText == title && titleInfo.isCustom) {
            found = true;
            break;
        }
    }
    
    if (!found) {
        m_availableTitles.emplace_back("Custom", title, -1, true);
    }
}

std::string TitleSystem::GetCurrentTitle() const
{
    if (m_currentTitleIndex >= 0 && m_currentTitleIndex < static_cast<int>(m_availableTitles.size())) {
        return m_availableTitles[m_currentTitleIndex].displayText;
    }
    return "";
}

bool TitleSystem::FindTitleFunctions()
{
    if (!g_pMod) return false;
    
    // These patterns would need to be found through reverse engineering
    // Using placeholder patterns for demonstration
    
    // Find get player title function
    uintptr_t getTitlePattern = g_pMod->FindPattern(
        "\x48\x89\x5C\x24\x08\x57\x48\x83\xEC\x20\x48\x8B\xF9\x48\x85\xC9",
        "xxxxxxxxxxxxxxxx"
    );
    
    if (getTitlePattern) {
        m_pGetPlayerTitleFunction = reinterpret_cast<void*>(getTitlePattern);
    }
    
    // Find set player title function
    uintptr_t setTitlePattern = g_pMod->FindPattern(
        "\x40\x53\x48\x83\xEC\x20\x48\x8B\xD9\x48\x85\xD2\x74\x00",
        "xxxxxxxxxxxxx?"
    );
    
    if (setTitlePattern) {
        m_pSetPlayerTitleFunction = reinterpret_cast<void*>(setTitlePattern);
    }
    
    // Find network title data functions
    uintptr_t sendTitlePattern = g_pMod->FindPattern(
        "\x48\x89\x5C\x24\x10\x48\x89\x74\x24\x18\x57\x48\x83\xEC\x20",
        "xxxxxxxxxxxxxxx"
    );
    
    if (sendTitlePattern) {
        m_pSendTitleDataFunction = reinterpret_cast<void*>(sendTitlePattern);
    }
    
    return (m_pGetPlayerTitleFunction != nullptr);
}

void TitleSystem::InitializeDefaultTitles()
{
    // Add common Rocket League titles
    m_availableTitles.clear();
    
    // Default titles (these would be the actual title IDs from the game)
    m_availableTitles.emplace_back("Rookie", "Rookie", 1);
    m_availableTitles.emplace_back("Semi-Pro", "Semi-Pro", 2);
    m_availableTitles.emplace_back("Pro", "Pro", 3);
    m_availableTitles.emplace_back("Veteran", "Veteran", 4);
    m_availableTitles.emplace_back("Expert", "Expert", 5);
    m_availableTitles.emplace_back("Master", "Master", 6);
    m_availableTitles.emplace_back("Legend", "Legend", 7);
    m_availableTitles.emplace_back("Rocketeer", "Rocketeer", 8);
    
    // Special titles
    m_availableTitles.emplace_back("Grand Champion", "Grand Champion", 100);
    m_availableTitles.emplace_back("Supersonic Legend", "Supersonic Legend", 101);
    m_availableTitles.emplace_back("Season 1 Grand Champion", "Season 1 Grand Champion", 200);
    m_availableTitles.emplace_back("RLCS Champion", "RLCS Champion", 300);
    m_availableTitles.emplace_back("Psyonix", "Psyonix", 999);
}

void TitleSystem::LoadCustomTitles()
{
    // Load custom titles from config file
    std::ifstream configFile("titles.json");
    if (configFile.is_open()) {
        try {
            json config;
            configFile >> config;
            
            if (config.contains("custom_titles")) {
                for (const auto& title : config["custom_titles"]) {
                    std::string titleText = title.get<std::string>();
                    m_availableTitles.emplace_back("Custom", titleText, -1, true);
                }
            }
        }
        catch (const std::exception&) {
            // Failed to parse config, continue with defaults
        }
        configFile.close();
    }
}

bool TitleSystem::IsLocalPlayer(void* playerObject)
{
    return (playerObject == m_pLocalPlayer);
}

void TitleSystem::OverridePlayerTitle(void* playerObject, const std::string& title)
{
    m_playerTitleOverrides[playerObject] = title;
}

// Hook implementations
const char* __fastcall TitleSystem::HookedGetPlayerTitle(void* playerObject)
{
    if (g_pTitleSystem && g_pTitleSystem->IsLocalPlayer(playerObject)) {
        // Return current selected title for local player
        static std::string currentTitle = g_pTitleSystem->GetCurrentTitle();
        currentTitle = g_pTitleSystem->GetCurrentTitle();
        return currentTitle.c_str();
    }
    
    // Check for overrides
    if (g_pTitleSystem) {
        auto it = g_pTitleSystem->m_playerTitleOverrides.find(playerObject);
        if (it != g_pTitleSystem->m_playerTitleOverrides.end()) {
            return it->second.c_str();
        }
    }
    
    // Call original function
    if (g_pTitleSystem && g_pTitleSystem->m_originalGetPlayerTitle) {
        return g_pTitleSystem->m_originalGetPlayerTitle(playerObject);
    }
    
    return "Rookie";
}

void __fastcall TitleSystem::HookedSetPlayerTitle(void* playerObject, const char* title)
{
    if (g_pTitleSystem && g_pTitleSystem->IsLocalPlayer(playerObject)) {
        // Intercept title setting for local player
        // Don't call original to prevent server sync issues
        return;
    }
    
    // Call original function for other players
    if (g_pTitleSystem && g_pTitleSystem->m_originalSetPlayerTitle) {
        g_pTitleSystem->m_originalSetPlayerTitle(playerObject, title);
    }
}

void __fastcall TitleSystem::HookedSendTitleData(void* networkManager, void* titleData)
{
    if (g_pTitleSystem) {
        g_pTitleSystem->ModifyOutgoingTitleData(titleData);
    }
    
    // Call original function
    if (g_pTitleSystem && g_pTitleSystem->m_originalSendTitleData) {
        g_pTitleSystem->m_originalSendTitleData(networkManager, titleData);
    }
}

void __fastcall TitleSystem::HookedReceiveTitleData(void* networkManager, void* titleData)
{
    if (g_pTitleSystem) {
        g_pTitleSystem->ModifyIncomingTitleData(titleData);
    }
    
    // Call original function
    if (g_pTitleSystem && g_pTitleSystem->m_originalReceiveTitleData) {
        g_pTitleSystem->m_originalReceiveTitleData(networkManager, titleData);
    }
}

void TitleSystem::ModifyOutgoingTitleData(void* titleData)
{
    if (!titleData) return;
    
    // Modify the title data being sent to server
    // This would require understanding the network packet structure
    // For now, this is a placeholder
}

void TitleSystem::ModifyIncomingTitleData(void* titleData)
{
    if (!titleData) return;
    
    // Modify incoming title data from server
    // This could be used to override other players' titles locally
}
