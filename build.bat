@echo off
echo Building Rocket League Mod...

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
cmake .. -G "Visual Studio 17 2022" -A x64

REM Build the project
cmake --build . --config Release

REM Copy result
if exist Release\RLMod.dll (
    copy Release\RLMod.dll ..\RLMod.dll
    echo Build successful! RLMod.dll created.
) else (
    echo Build failed!
)

cd ..
pause
