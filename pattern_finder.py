#!/usr/bin/env python3
"""
Pattern Finder Tool
Helps find memory patterns for different Rocket League versions
"""

import pymem
import psutil
import struct
import time
from typing import List, Dict, Optional, Tuple


class PatternFinder:
    """Tool to find and test memory patterns"""
    
    def __init__(self):
        self.process: Optional[pymem.Pymem] = None
        self.base_address: Optional[int] = None
        self.module_size: Optional[int] = None
        
    def connect_to_game(self) -> bool:
        """Connect to Rocket League"""
        try:
            # Find Rocket League process
            for proc in psutil.process_iter(['name']):
                if 'rocket' in proc.info['name'].lower():
                    process_name = proc.info['name']
                    break
            else:
                print("❌ Rocket League not found")
                return False
            
            # Connect to process
            self.process = pymem.Pymem(process_name)
            
            # Get module info
            modules = list(self.process.list_modules())
            main_module = modules[0]
            self.base_address = main_module.lpBaseOfDll
            self.module_size = main_module.SizeOfImage
            
            print(f"✅ Connected to {process_name}")
            print(f"📍 Base: 0x{self.base_address:X}")
            print(f"📏 Size: 0x{self.module_size:X}")
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def search_strings(self, search_terms: List[str]) -> Dict[str, List[int]]:
        """Search for strings in memory"""
        results = {}
        
        print("🔍 Searching for strings...")
        
        for term in search_terms:
            print(f"  Searching: '{term}'")
            addresses = []
            
            # Search for UTF-8 encoding
            pattern = term.encode('utf-8')
            found = self._search_pattern(pattern)
            addresses.extend(found)
            
            # Search for UTF-16 encoding
            pattern = term.encode('utf-16le')
            found = self._search_pattern(pattern)
            addresses.extend(found)
            
            results[term] = addresses
            print(f"    Found {len(addresses)} occurrences")
        
        return results
    
    def search_car_patterns(self) -> Dict[str, List[int]]:
        """Search for car-related patterns"""
        print("🚗 Searching for car patterns...")
        
        patterns = {
            'visibility_byte': [
                b'\x88\x44\x24\x20',  # mov [rsp+20], al
                b'\x88\x47\x00',      # mov [rdi], al
                b'\x88\x46\x00',      # mov [rsi], al
            ],
            'render_call': [
                b'\xE8\x00\x00\x00\x00\x48\x8B',  # call + mov
                b'\xFF\x15\x00\x00\x00\x00\x48',  # call [ptr] + mov
            ],
            'mesh_ptr': [
                b'\x48\x8B\x40\x00\x48\x85\xC0',  # mov rax, [rax+?]; test rax, rax
                b'\x48\x8B\x48\x00\x48\x85\xC9',  # mov rcx, [rax+?]; test rcx, rcx
            ]
        }
        
        results = {}
        for name, pattern_list in patterns.items():
            print(f"  Searching: {name}")
            all_addresses = []
            
            for pattern in pattern_list:
                found = self._search_pattern_with_wildcards(pattern)
                all_addresses.extend(found)
            
            results[name] = all_addresses
            print(f"    Found {len(all_addresses)} matches")
        
        return results
    
    def search_title_patterns(self) -> Dict[str, List[int]]:
        """Search for title-related patterns"""
        print("🏆 Searching for title patterns...")
        
        # Common title strings to search for
        title_strings = [
            "Rookie", "Semi-Pro", "Pro", "Veteran", "Expert",
            "Master", "Legend", "Rocketeer", "Grand Champion",
            "Supersonic Legend", "RLCS", "Psyonix"
        ]
        
        string_results = self.search_strings(title_strings)
        
        # Look for patterns near title strings
        patterns = {}
        for title, addresses in string_results.items():
            if addresses:
                print(f"  Analyzing {title} references...")
                patterns[f"{title}_refs"] = self._analyze_string_references(addresses[0])
        
        return patterns
    
    def _search_pattern(self, pattern: bytes) -> List[int]:
        """Search for exact pattern"""
        addresses = []
        chunk_size = 0x10000  # 64KB chunks
        
        try:
            for offset in range(0, self.module_size, chunk_size):
                if offset + len(pattern) > self.module_size:
                    break
                
                try:
                    chunk = self.process.read_bytes(self.base_address + offset, 
                                                  min(chunk_size, self.module_size - offset))
                    
                    pos = 0
                    while True:
                        pos = chunk.find(pattern, pos)
                        if pos == -1:
                            break
                        addresses.append(self.base_address + offset + pos)
                        pos += 1
                        
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"Search error: {e}")
        
        return addresses
    
    def _search_pattern_with_wildcards(self, pattern: bytes) -> List[int]:
        """Search pattern with wildcards (0x00 = wildcard)"""
        addresses = []
        chunk_size = 0x10000
        
        try:
            for offset in range(0, self.module_size, chunk_size):
                if offset + len(pattern) > self.module_size:
                    break
                
                try:
                    chunk = self.process.read_bytes(self.base_address + offset,
                                                  min(chunk_size, self.module_size - offset))
                    
                    for i in range(len(chunk) - len(pattern)):
                        match = True
                        for j in range(len(pattern)):
                            if pattern[j] != 0 and chunk[i + j] != pattern[j]:
                                match = False
                                break
                        
                        if match:
                            addresses.append(self.base_address + offset + i)
                            
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"Wildcard search error: {e}")
        
        return addresses
    
    def _analyze_string_references(self, string_addr: int) -> List[int]:
        """Find code that references a string"""
        references = []
        
        # Look for LEA instructions that reference this string
        # LEA pattern: 48 8D 15 XX XX XX XX (lea rdx, [rip+offset])
        lea_pattern = b'\x48\x8D\x15'
        
        chunk_size = 0x10000
        for offset in range(0, self.module_size, chunk_size):
            try:
                chunk = self.process.read_bytes(self.base_address + offset,
                                              min(chunk_size, self.module_size - offset))
                
                pos = 0
                while True:
                    pos = chunk.find(lea_pattern, pos)
                    if pos == -1:
                        break
                    
                    if pos + 7 <= len(chunk):
                        # Calculate RIP-relative address
                        instruction_addr = self.base_address + offset + pos
                        rel_offset = struct.unpack('<i', chunk[pos+3:pos+7])[0]
                        target_addr = instruction_addr + 7 + rel_offset
                        
                        # Check if this points to our string
                        if abs(target_addr - string_addr) < 0x1000:
                            references.append(instruction_addr)
                    
                    pos += 1
                    
            except Exception:
                continue
        
        return references
    
    def test_pattern_at_address(self, address: int, size: int = 32) -> str:
        """Test what's at a specific address"""
        try:
            data = self.process.read_bytes(address, size)
            
            # Try to interpret as string
            try:
                string_data = data.split(b'\x00')[0].decode('utf-8', errors='ignore')
                if len(string_data) > 3 and string_data.isprintable():
                    return f"String: '{string_data}'"
            except:
                pass
            
            # Show as hex
            hex_data = ' '.join(f'{b:02X}' for b in data[:16])
            return f"Hex: {hex_data}"
            
        except Exception as e:
            return f"Error: {e}"
    
    def generate_updated_patterns(self, results: Dict[str, List[int]]) -> str:
        """Generate updated pattern code"""
        code = "# Updated patterns found by pattern_finder.py\n\n"
        
        code += "# Car invisibility patterns\n"
        code += "self.visibility_patterns = {\n"
        
        if 'visibility_byte' in results and results['visibility_byte']:
            addr = results['visibility_byte'][0]
            code += f"    'car_visibility_flag': {{\n"
            code += f"        'address': 0x{addr:X},\n"
            code += f"        'offset': 0\n"
            code += f"    }},\n"
        
        code += "}\n\n"
        
        code += "# Title system patterns\n"
        code += "self.title_patterns = {\n"
        
        for key, addresses in results.items():
            if 'refs' in key and addresses:
                addr = addresses[0]
                code += f"    '{key}': {{\n"
                code += f"        'address': 0x{addr:X},\n"
                code += f"        'offset': 0\n"
                code += f"    }},\n"
        
        code += "}\n"
        
        return code


def main():
    """Main pattern finder function"""
    print("🔍 Rocket League Pattern Finder")
    print("=" * 40)
    
    finder = PatternFinder()
    
    if not finder.connect_to_game():
        input("Press Enter to exit...")
        return
    
    print("\n🎯 What would you like to search for?")
    print("1. Car invisibility patterns")
    print("2. Title system patterns")
    print("3. Custom string search")
    print("4. Full scan (all patterns)")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    results = {}
    
    if choice == "1":
        results.update(finder.search_car_patterns())
    elif choice == "2":
        results.update(finder.search_title_patterns())
    elif choice == "3":
        terms = input("Enter strings to search (comma-separated): ").split(',')
        terms = [term.strip() for term in terms if term.strip()]
        results.update(finder.search_strings(terms))
    elif choice == "4":
        results.update(finder.search_car_patterns())
        results.update(finder.search_title_patterns())
    else:
        print("Invalid choice")
        return
    
    # Display results
    print("\n📊 RESULTS:")
    print("=" * 40)
    
    for pattern_name, addresses in results.items():
        print(f"\n🔍 {pattern_name}:")
        if addresses:
            for i, addr in enumerate(addresses[:5]):  # Show first 5 results
                test_result = finder.test_pattern_at_address(addr)
                print(f"  0x{addr:X} - {test_result}")
            if len(addresses) > 5:
                print(f"  ... and {len(addresses) - 5} more")
        else:
            print("  No matches found")
    
    # Generate code
    if results:
        print("\n📝 Generating updated pattern code...")
        updated_code = finder.generate_updated_patterns(results)
        
        with open("updated_patterns.txt", "w") as f:
            f.write(updated_code)
        
        print("✅ Updated patterns saved to 'updated_patterns.txt'")
        print("\n💡 Copy the patterns from the file and update your mod files!")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
