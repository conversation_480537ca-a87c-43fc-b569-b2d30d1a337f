#!/usr/bin/env python3
"""
Test the found patterns to see if they work
"""

import pymem
import psutil
import time


def test_patterns():
    """Test if the found patterns work"""
    print("Testing Found Patterns")
    print("=" * 25)
    
    # Found patterns from the updater
    patterns = {
        'car_visibility': 0x7FF708CC6B18,
        'title_string': 0x7FF708C04718,
        'player_data': 0x7FF708C5FE16
    }
    
    try:
        # Connect to Rocket League
        print("[*] Connecting to Rocket League...")
        process = pymem.Pymem("RocketLeague.exe")
        print(f"[+] Connected! PID: {process.process_id}")
        
        # Test each pattern
        for name, address in patterns.items():
            print(f"\n[*] Testing {name} at 0x{address:X}")
            
            try:
                # Try to read from the address
                data = process.read_bytes(address, 16)
                hex_data = ' '.join(f'{b:02X}' for b in data[:8])
                print(f"    [+] Read successful: {hex_data}")
                
                # Try to interpret as string
                try:
                    string_data = data.split(b'\x00')[0].decode('utf-8', errors='ignore')
                    if len(string_data) > 2 and string_data.isprintable():
                        print(f"    [+] String data: '{string_data}'")
                except:
                    pass
                
                # Test write (safe test)
                original = process.read_bytes(address, 1)
                if process.write_bytes(address, original, 1):
                    print(f"    [+] Write test successful")
                else:
                    print(f"    [-] Write test failed")
                    
            except Exception as e:
                print(f"    [!] Error testing {name}: {e}")
        
        print(f"\n[+] Pattern testing completed!")
        
    except Exception as e:
        print(f"[!] Error: {e}")


def test_car_invisibility():
    """Test car invisibility specifically"""
    print("\nTesting Car Invisibility")
    print("=" * 25)
    
    try:
        process = pymem.Pymem("RocketLeague.exe")
        car_addr = 0x7FF708CC6B18
        
        print(f"[*] Testing car visibility at 0x{car_addr:X}")
        
        # Read current value
        current = process.read_bytes(car_addr, 1)[0]
        print(f"[*] Current value: 0x{current:02X}")
        
        # Test toggle
        new_value = 0x00 if current != 0x00 else 0x01
        print(f"[*] Setting to: 0x{new_value:02X}")
        
        if process.write_bytes(car_addr, bytes([new_value]), 1):
            print("[+] Write successful!")
            
            # Read back to verify
            time.sleep(0.1)
            verify = process.read_bytes(car_addr, 1)[0]
            print(f"[*] Verified value: 0x{verify:02X}")
            
            if verify == new_value:
                print("[+] Car invisibility toggle working!")
            else:
                print("[-] Value didn't stick")
                
            # Restore original
            process.write_bytes(car_addr, bytes([current]), 1)
            print("[*] Restored original value")
            
        else:
            print("[-] Write failed")
            
    except Exception as e:
        print(f"[!] Error: {e}")


def main():
    """Main test function"""
    print("Rocket League Pattern Tester")
    print("=" * 30)
    
    # Check if game is running
    try:
        for proc in psutil.process_iter(['name']):
            if 'rocket' in proc.info['name'].lower():
                print(f"[+] Found Rocket League: {proc.info['name']}")
                break
        else:
            print("[!] Rocket League not found!")
            return
    except Exception as e:
        print(f"[!] Error checking processes: {e}")
        return
    
    # Run tests
    test_patterns()
    test_car_invisibility()
    
    print("\n[*] Testing completed!")
    input("Press Enter to exit...")


if __name__ == "__main__":
    main()
