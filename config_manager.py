"""
Configuration Manager - Handles settings and configuration persistence
"""

import json
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import configparser


@dataclass
class HotkeyConfig:
    """Hotkey configuration"""
    toggle_gui: str = "f3"
    toggle_car_invisibility: str = "f1"
    cycle_title: str = "f2"
    emergency_restore: str = "f4"


@dataclass
class UIConfig:
    """UI configuration"""
    theme: str = "dark"
    window_width: int = 800
    window_height: int = 600
    window_x: int = 100
    window_y: int = 100
    auto_hide: bool = False
    minimize_to_tray: bool = True
    show_on_startup: bool = False


@dataclass
class GameConfig:
    """Game-related configuration"""
    auto_connect: bool = True
    connection_timeout: int = 30
    memory_scan_timeout: int = 10
    process_names: List[str] = None
    
    def __post_init__(self):
        if self.process_names is None:
            self.process_names = ["RocketLeague.exe", "RocketLeague", "rl.exe"]


@dataclass
class CarConfig:
    """Car invisibility configuration"""
    remember_state: bool = True
    last_invisible_state: bool = False
    restore_on_exit: bool = True
    backup_original_values: bool = True


@dataclass
class TitleConfig:
    """Title system configuration"""
    remember_selection: bool = True
    last_selected_index: int = 0
    custom_titles: List[str] = None
    auto_apply_on_connect: bool = False
    
    def __post_init__(self):
        if self.custom_titles is None:
            self.custom_titles = []


@dataclass
class ModConfig:
    """Main mod configuration"""
    hotkeys: HotkeyConfig
    ui: UIConfig
    game: GameConfig
    car: CarConfig
    title: TitleConfig
    
    # General settings
    debug_mode: bool = False
    log_level: str = "INFO"
    auto_update_check: bool = True
    
    def __init__(self):
        self.hotkeys = HotkeyConfig()
        self.ui = UIConfig()
        self.game = GameConfig()
        self.car = CarConfig()
        self.title = TitleConfig()


class ConfigManager:
    """Manages configuration loading, saving, and validation"""
    
    def __init__(self, config_file: str = "rl_mod_config.json"):
        self.config_file = config_file
        self.config = ModConfig()
        self.config_loaded = False
        
        # Create config directory if it doesn't exist
        self.config_dir = os.path.dirname(os.path.abspath(config_file))
        if self.config_dir and not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if not os.path.exists(self.config_file):
                print(f"📄 Config file not found, creating default: {self.config_file}")
                self.save_config()
                self.config_loaded = True
                return True
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Load configuration sections
            self._load_hotkeys(config_data.get('hotkeys', {}))
            self._load_ui_config(config_data.get('ui', {}))
            self._load_game_config(config_data.get('game', {}))
            self._load_car_config(config_data.get('car', {}))
            self._load_title_config(config_data.get('title', {}))
            
            # Load general settings
            self.config.debug_mode = config_data.get('debug_mode', False)
            self.config.log_level = config_data.get('log_level', 'INFO')
            self.config.auto_update_check = config_data.get('auto_update_check', True)
            
            self.config_loaded = True
            print(f"✅ Configuration loaded from {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            print("📄 Using default configuration")
            self.config = ModConfig()
            self.config_loaded = True
            return False
    
    def save_config(self) -> bool:
        """Save configuration to file"""
        try:
            config_data = {
                'hotkeys': asdict(self.config.hotkeys),
                'ui': asdict(self.config.ui),
                'game': asdict(self.config.game),
                'car': asdict(self.config.car),
                'title': asdict(self.config.title),
                'debug_mode': self.config.debug_mode,
                'log_level': self.config.log_level,
                'auto_update_check': self.config.auto_update_check,
                '_metadata': {
                    'version': '1.0',
                    'created_by': 'Rocket League Mod',
                    'last_modified': self._get_timestamp()
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Configuration saved to {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving config: {e}")
            return False
    
    def _load_hotkeys(self, hotkey_data: Dict[str, Any]):
        """Load hotkey configuration"""
        self.config.hotkeys.toggle_gui = hotkey_data.get('toggle_gui', 'f3')
        self.config.hotkeys.toggle_car_invisibility = hotkey_data.get('toggle_car_invisibility', 'f1')
        self.config.hotkeys.cycle_title = hotkey_data.get('cycle_title', 'f2')
        self.config.hotkeys.emergency_restore = hotkey_data.get('emergency_restore', 'f4')
    
    def _load_ui_config(self, ui_data: Dict[str, Any]):
        """Load UI configuration"""
        self.config.ui.theme = ui_data.get('theme', 'dark')
        self.config.ui.window_width = ui_data.get('window_width', 800)
        self.config.ui.window_height = ui_data.get('window_height', 600)
        self.config.ui.window_x = ui_data.get('window_x', 100)
        self.config.ui.window_y = ui_data.get('window_y', 100)
        self.config.ui.auto_hide = ui_data.get('auto_hide', False)
        self.config.ui.minimize_to_tray = ui_data.get('minimize_to_tray', True)
        self.config.ui.show_on_startup = ui_data.get('show_on_startup', False)
    
    def _load_game_config(self, game_data: Dict[str, Any]):
        """Load game configuration"""
        self.config.game.auto_connect = game_data.get('auto_connect', True)
        self.config.game.connection_timeout = game_data.get('connection_timeout', 30)
        self.config.game.memory_scan_timeout = game_data.get('memory_scan_timeout', 10)
        self.config.game.process_names = game_data.get('process_names', 
                                                      ["RocketLeague.exe", "RocketLeague", "rl.exe"])
    
    def _load_car_config(self, car_data: Dict[str, Any]):
        """Load car configuration"""
        self.config.car.remember_state = car_data.get('remember_state', True)
        self.config.car.last_invisible_state = car_data.get('last_invisible_state', False)
        self.config.car.restore_on_exit = car_data.get('restore_on_exit', True)
        self.config.car.backup_original_values = car_data.get('backup_original_values', True)
    
    def _load_title_config(self, title_data: Dict[str, Any]):
        """Load title configuration"""
        self.config.title.remember_selection = title_data.get('remember_selection', True)
        self.config.title.last_selected_index = title_data.get('last_selected_index', 0)
        self.config.title.custom_titles = title_data.get('custom_titles', [])
        self.config.title.auto_apply_on_connect = title_data.get('auto_apply_on_connect', False)
    
    def get_config(self) -> ModConfig:
        """Get the current configuration"""
        return self.config
    
    def update_hotkey(self, action: str, key: str) -> bool:
        """Update a hotkey binding"""
        try:
            if hasattr(self.config.hotkeys, action):
                setattr(self.config.hotkeys, action, key.lower())
                self.save_config()
                return True
            return False
        except Exception as e:
            print(f"Error updating hotkey: {e}")
            return False
    
    def update_ui_position(self, x: int, y: int, width: int, height: int):
        """Update UI window position and size"""
        self.config.ui.window_x = x
        self.config.ui.window_y = y
        self.config.ui.window_width = width
        self.config.ui.window_height = height
        self.save_config()
    
    def update_car_state(self, invisible: bool):
        """Update last car invisibility state"""
        self.config.car.last_invisible_state = invisible
        if self.config.car.remember_state:
            self.save_config()
    
    def update_title_selection(self, index: int):
        """Update last selected title index"""
        self.config.title.last_selected_index = index
        if self.config.title.remember_selection:
            self.save_config()
    
    def add_custom_title(self, title: str) -> bool:
        """Add a custom title"""
        if title not in self.config.title.custom_titles:
            self.config.title.custom_titles.append(title)
            self.save_config()
            return True
        return False
    
    def remove_custom_title(self, title: str) -> bool:
        """Remove a custom title"""
        if title in self.config.title.custom_titles:
            self.config.title.custom_titles.remove(title)
            self.save_config()
            return True
        return False
    
    def get_hotkey(self, action: str) -> Optional[str]:
        """Get hotkey for an action"""
        return getattr(self.config.hotkeys, action, None)
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Validate hotkeys
        hotkeys = [
            self.config.hotkeys.toggle_gui,
            self.config.hotkeys.toggle_car_invisibility,
            self.config.hotkeys.cycle_title,
            self.config.hotkeys.emergency_restore
        ]
        
        if len(set(hotkeys)) != len(hotkeys):
            issues.append("Duplicate hotkey bindings detected")
        
        # Validate UI settings
        if self.config.ui.window_width < 400:
            issues.append("Window width too small (minimum 400)")
        
        if self.config.ui.window_height < 300:
            issues.append("Window height too small (minimum 300)")
        
        # Validate game settings
        if self.config.game.connection_timeout < 5:
            issues.append("Connection timeout too short (minimum 5 seconds)")
        
        if not self.config.game.process_names:
            issues.append("No process names configured")
        
        return issues
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.config = ModConfig()
        self.save_config()
        print("🔄 Configuration reset to defaults")
    
    def export_config(self, export_file: str) -> bool:
        """Export configuration to a different file"""
        try:
            import shutil
            shutil.copy2(self.config_file, export_file)
            print(f"📤 Configuration exported to {export_file}")
            return True
        except Exception as e:
            print(f"❌ Error exporting config: {e}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """Import configuration from a file"""
        try:
            import shutil
            shutil.copy2(import_file, self.config_file)
            return self.load_config()
        except Exception as e:
            print(f"❌ Error importing config: {e}")
            return False
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        import datetime
        return datetime.datetime.now().isoformat()
    
    def get_config_info(self) -> Dict[str, Any]:
        """Get configuration file information"""
        try:
            stat = os.stat(self.config_file)
            return {
                'file_path': os.path.abspath(self.config_file),
                'file_size': stat.st_size,
                'last_modified': stat.st_mtime,
                'exists': True,
                'loaded': self.config_loaded
            }
        except FileNotFoundError:
            return {
                'file_path': os.path.abspath(self.config_file),
                'exists': False,
                'loaded': self.config_loaded
            }
