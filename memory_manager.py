"""
Rocket League Memory Manager
Handles all memory operations for the mod
"""

import pymem
import psutil
import ctypes
import struct
import time
from typing import Optional, List, Tuple, Any
from dataclasses import dataclass


@dataclass
class MemoryPattern:
    """Represents a memory pattern to search for"""
    pattern: bytes
    mask: str
    offset: int = 0
    name: str = ""


class RocketLeagueMemory:
    """Main memory interface for Rocket League"""
    
    def __init__(self):
        self.process: Optional[pymem.Pymem] = None
        self.process_id: Optional[int] = None
        self.base_address: Optional[int] = None
        self.is_connected = False
        
        # Memory addresses (will be found dynamically)
        self.addresses = {
            'car_visibility': None,
            'player_title': None,
            'local_player': None,
            'game_instance': None,
        }
        
        # Known patterns for different game versions
        self.patterns = {
            'car_render': MemoryPattern(
                pattern=b'\x48\x89\x5C\x24\x08\x48\x89\x74\x24\x10\x57\x48\x83\xEC\x20',
                mask='xxxxxxxxxxxxxxx',
                name='car_render_function'
            ),
            'player_title': MemoryPattern(
                pattern=b'\x48\x8B\x05\x00\x00\x00\x00\x48\x85\xC0\x74\x00\x48\x8B\x40',
                mask='xxx????xxxx?xxx',
                name='player_title_function'
            ),
            'local_player': MemoryPattern(
                pattern=b'\x48\x8B\x0D\x00\x00\x00\x00\x48\x85\xC9\x74\x00\x48\x8B\x01',
                mask='xxx????xxxx?xxx',
                name='local_player_pointer'
            )
        }
    
    def connect_to_game(self) -> bool:
        """Connect to Rocket League process"""
        try:
            # Find Rocket League process
            process_name = self._find_rocket_league_process()
            if not process_name:
                return False
            
            # Connect to process
            self.process = pymem.Pymem(process_name)
            self.process_id = self.process.process_id
            self.base_address = self.process.base_address
            self.is_connected = True
            
            print(f"✅ Connected to {process_name} (PID: {self.process_id})")
            print(f"📍 Base address: 0x{self.base_address:X}")
            
            # Find memory addresses
            self._scan_memory_patterns()
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to connect to Rocket League: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the process"""
        if self.process:
            self.process.close_process()
        self.is_connected = False
        self.process = None
        print("🔌 Disconnected from Rocket League")
    
    def _find_rocket_league_process(self) -> Optional[str]:
        """Find Rocket League process name"""
        possible_names = [
            "RocketLeague.exe",
            "RocketLeague",
            "rl.exe"
        ]
        
        for proc in psutil.process_iter(['name']):
            try:
                proc_name = proc.info['name']
                if any(name.lower() in proc_name.lower() for name in possible_names):
                    return proc_name
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return None
    
    def _scan_memory_patterns(self):
        """Scan for memory patterns to find important addresses"""
        print("🔍 Scanning for memory patterns...")
        
        for pattern_name, pattern in self.patterns.items():
            try:
                address = self._pattern_scan(pattern.pattern, pattern.mask)
                if address:
                    self.addresses[pattern_name] = address + pattern.offset
                    print(f"✅ Found {pattern_name}: 0x{address:X}")
                else:
                    print(f"❌ Pattern not found: {pattern_name}")
            except Exception as e:
                print(f"❌ Error scanning {pattern_name}: {e}")
    
    def _pattern_scan(self, pattern: bytes, mask: str) -> Optional[int]:
        """Scan memory for a specific pattern"""
        if not self.process:
            return None
        
        try:
            # Get module information
            modules = list(self.process.list_modules())
            main_module = modules[0]  # Main executable
            
            start_address = main_module.lpBaseOfDll
            end_address = start_address + main_module.SizeOfImage
            
            # Scan in chunks to avoid memory issues
            chunk_size = 0x1000  # 4KB chunks
            current_address = start_address
            
            while current_address < end_address:
                try:
                    # Read memory chunk
                    chunk = self.process.read_bytes(current_address, chunk_size)
                    
                    # Search for pattern in chunk
                    for i in range(len(chunk) - len(pattern)):
                        match = True
                        for j in range(len(pattern)):
                            if mask[j] != '?' and chunk[i + j] != pattern[j]:
                                match = False
                                break
                        
                        if match:
                            return current_address + i
                    
                    current_address += chunk_size
                    
                except Exception:
                    current_address += chunk_size
                    continue
            
            return None
            
        except Exception as e:
            print(f"Pattern scan error: {e}")
            return None
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from address"""
        if not self.is_connected or not self.process:
            return None
        
        try:
            return self.process.read_bytes(address, size)
        except Exception as e:
            print(f"Read memory error at 0x{address:X}: {e}")
            return None
    
    def write_memory(self, address: int, data: bytes) -> bool:
        """Write memory to address"""
        if not self.is_connected or not self.process:
            return False
        
        try:
            self.process.write_bytes(address, data, len(data))
            return True
        except Exception as e:
            print(f"Write memory error at 0x{address:X}: {e}")
            return False
    
    def read_int(self, address: int) -> Optional[int]:
        """Read 4-byte integer from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<I', data)[0]
        return None
    
    def write_int(self, address: int, value: int) -> bool:
        """Write 4-byte integer to memory"""
        data = struct.pack('<I', value)
        return self.write_memory(address, data)
    
    def read_float(self, address: int) -> Optional[float]:
        """Read 4-byte float from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return None
    
    def write_float(self, address: int, value: float) -> bool:
        """Write 4-byte float to memory"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)
    
    def read_string(self, address: int, length: int = 256) -> Optional[str]:
        """Read string from memory"""
        data = self.read_memory(address, length)
        if data:
            try:
                # Find null terminator
                null_pos = data.find(b'\x00')
                if null_pos != -1:
                    data = data[:null_pos]
                return data.decode('utf-8', errors='ignore')
            except Exception:
                return None
        return None
    
    def write_string(self, address: int, value: str) -> bool:
        """Write string to memory"""
        try:
            data = value.encode('utf-8') + b'\x00'
            return self.write_memory(address, data)
        except Exception:
            return False
    
    def is_process_running(self) -> bool:
        """Check if the connected process is still running"""
        if not self.process_id:
            return False
        
        try:
            proc = psutil.Process(self.process_id)
            return proc.is_running()
        except psutil.NoSuchProcess:
            return False
    
    def get_module_address(self, module_name: str) -> Optional[int]:
        """Get base address of a specific module"""
        if not self.process:
            return None
        
        try:
            for module in self.process.list_modules():
                if module_name.lower() in module.name.lower():
                    return module.lpBaseOfDll
        except Exception:
            pass
        
        return None
