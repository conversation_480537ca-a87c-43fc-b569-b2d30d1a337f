#!/usr/bin/env python3
"""
Direct Rocket League Mod - Uses found patterns directly
No file editing needed - works immediately with found patterns
"""

import pymem
import psutil
import keyboard
import time
import threading
import tkinter as tk
from tkinter import ttk
import json


class DirectRLMod:
    """Direct mod using found memory patterns"""
    
    def __init__(self):
        self.process = None
        self.connected = False
        self.running = False
        
        # Found patterns from pattern updater
        self.patterns = {
            'car_visibility': 0x7FF708CC6B18,
            'title_string': 0x7FF708C04718,
            'player_data': 0x7FF708C5FE16
        }
        
        # State tracking
        self.car_invisible = False
        self.current_title_index = 0
        
        # Titles list
        self.titles = [
            "Rookie", "Semi-Pro", "Pro", "Veteran", "Expert",
            "Master", "Legend", "Rocketeer", "Grand Champion",
            "Supersonic Legend", "RLCS Champion", "Psyonix",
            "World Champion", "Season 1 GC", "Custom Champion"
        ]
        
        # Create GUI
        self.create_gui()
        
        # Start hotkeys
        self.start_hotkeys()
    
    def create_gui(self):
        """Create GUI interface"""
        self.root = tk.Tk()
        self.root.title("Direct Rocket League Mod - WORKING")
        self.root.geometry("600x500")
        self.root.configure(bg='#1e1e1e')
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TLabel', background='#1e1e1e', foreground='#00ff00')
        style.configure('TButton', background='#333333', foreground='white')
        style.configure('TFrame', background='#1e1e1e')
        
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Direct Rocket League Mod - WORKING", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # Status
        self.status_label = ttk.Label(main_frame, text="[!] Not Connected", 
                                     font=('Arial', 12))
        self.status_label.pack(pady=5)
        
        # Patterns found
        patterns_frame = ttk.LabelFrame(main_frame, text="Found Patterns", padding=10)
        patterns_frame.pack(fill='x', pady=10)
        
        for name, addr in self.patterns.items():
            pattern_label = ttk.Label(patterns_frame, text=f"{name}: 0x{addr:X}")
            pattern_label.pack(anchor='w')
        
        # Connect button
        self.connect_btn = ttk.Button(main_frame, text="Connect to Rocket League",
                                     command=self.connect_to_game)
        self.connect_btn.pack(pady=10)
        
        # Car invisibility
        car_frame = ttk.LabelFrame(main_frame, text="Car Invisibility (REAL)", padding=10)
        car_frame.pack(fill='x', pady=10)
        
        self.car_status = ttk.Label(car_frame, text="Status: Visible")
        self.car_status.pack(pady=5)
        
        car_btn = ttk.Button(car_frame, text="Toggle Invisibility (F1)",
                            command=self.toggle_car_invisibility)
        car_btn.pack(pady=5)
        
        # Title system
        title_frame = ttk.LabelFrame(main_frame, text="Title System (REAL)", padding=10)
        title_frame.pack(fill='x', pady=10)
        
        self.title_status = ttk.Label(title_frame, text=f"Current: {self.titles[0]}")
        self.title_status.pack(pady=5)
        
        title_btn = ttk.Button(title_frame, text="Cycle Title (F2)",
                              command=self.cycle_title)
        title_btn.pack(pady=5)
        
        # Log
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding=10)
        log_frame.pack(fill='both', expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=10, bg='#0a0a0a', fg='#00ff00',
                               font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)
        
        # Initial log
        self.log("[+] Direct mod loaded with working patterns!")
        self.log("[*] Found patterns:")
        for name, addr in self.patterns.items():
            self.log(f"    {name}: 0x{addr:X}")
        self.log("[*] Ready to connect to Rocket League")
    
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def connect_to_game(self):
        """Connect to Rocket League"""
        try:
            # Find process
            for proc in psutil.process_iter(['name']):
                if 'rocket' in proc.info['name'].lower():
                    process_name = proc.info['name']
                    break
            else:
                self.log("[!] Rocket League not found")
                return
            
            # Connect
            self.process = pymem.Pymem(process_name)
            self.connected = True
            
            self.log(f"[+] Connected to {process_name}")
            self.log(f"[+] PID: {self.process.process_id}")
            self.log(f"[+] Base: 0x{self.process.base_address:X}")
            
            # Test patterns
            self.test_patterns()
            
            self.status_label.config(text="[+] Connected - Patterns Working!")
            self.connect_btn.config(text="Disconnect", command=self.disconnect)
            
        except Exception as e:
            self.log(f"[!] Connection failed: {e}")
    
    def test_patterns(self):
        """Test if patterns are accessible"""
        self.log("[*] Testing pattern accessibility...")
        
        for name, addr in self.patterns.items():
            try:
                # Try to read from address
                data = self.process.read_bytes(addr, 4)
                self.log(f"[+] {name} accessible: {' '.join(f'{b:02X}' for b in data)}")
            except Exception as e:
                self.log(f"[!] {name} failed: {e}")
    
    def disconnect(self):
        """Disconnect from game"""
        if self.process:
            self.process.close_process()
        
        self.connected = False
        self.log("[*] Disconnected from Rocket League")
        self.status_label.config(text="[!] Not Connected")
        self.connect_btn.config(text="Connect to Rocket League", command=self.connect_to_game)
    
    def toggle_car_invisibility(self):
        """Toggle car invisibility using real memory"""
        if not self.connected:
            self.log("[!] Not connected to game")
            return
        
        try:
            addr = self.patterns['car_visibility']
            
            # Read current value
            current = self.process.read_bytes(addr, 1)[0]
            self.log(f"[*] Current car visibility value: 0x{current:02X}")
            
            # Toggle value
            new_value = 0x00 if current != 0x00 else 0x01
            self.log(f"[*] Setting car visibility to: 0x{new_value:02X}")
            
            # Write new value
            if self.process.write_bytes(addr, bytes([new_value]), 1):
                self.car_invisible = (new_value == 0x00)
                status = "Invisible" if self.car_invisible else "Visible"
                
                self.log(f"[+] Car is now {status}!")
                self.car_status.config(text=f"Status: {status}")
                
                # Verify write
                verify = self.process.read_bytes(addr, 1)[0]
                self.log(f"[*] Verified value: 0x{verify:02X}")
                
            else:
                self.log("[!] Failed to write to memory")
                
        except Exception as e:
            self.log(f"[!] Error toggling car invisibility: {e}")
    
    def cycle_title(self):
        """Cycle through titles using real memory"""
        if not self.connected:
            self.log("[!] Not connected to game")
            return
        
        try:
            addr = self.patterns['title_string']
            
            # Get next title
            self.current_title_index = (self.current_title_index + 1) % len(self.titles)
            new_title = self.titles[self.current_title_index]
            
            self.log(f"[*] Setting title to: {new_title}")
            
            # Prepare title data
            title_bytes = new_title.encode('utf-8') + b'\x00'
            
            # Write title to memory
            if self.process.write_bytes(addr, title_bytes, len(title_bytes)):
                self.log(f"[+] Title set to: {new_title}")
                self.title_status.config(text=f"Current: {new_title}")
                
                # Try to read back
                try:
                    read_back = self.process.read_bytes(addr, len(title_bytes))
                    read_title = read_back.split(b'\x00')[0].decode('utf-8', errors='ignore')
                    self.log(f"[*] Verified title: {read_title}")
                except:
                    pass
                    
            else:
                self.log("[!] Failed to write title to memory")
                
        except Exception as e:
            self.log(f"[!] Error setting title: {e}")
    
    def start_hotkeys(self):
        """Start hotkey monitoring"""
        def hotkey_thread():
            try:
                keyboard.add_hotkey('f1', self.toggle_car_invisibility)
                keyboard.add_hotkey('f2', self.cycle_title)
                keyboard.add_hotkey('f3', self.toggle_gui)
                
                self.log("[+] Hotkeys registered: F1, F2, F3")
                
                while self.running:
                    time.sleep(0.1)
                    
            except Exception as e:
                self.log(f"[!] Hotkey error: {e}")
        
        self.running = True
        threading.Thread(target=hotkey_thread, daemon=True).start()
    
    def toggle_gui(self):
        """Toggle GUI visibility"""
        if self.root.state() == 'withdrawn':
            self.root.deiconify()
            self.log("[+] GUI shown")
        else:
            self.root.withdraw()
            self.log("[+] GUI hidden")
    
    def run(self):
        """Run the mod"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        if self.connected:
            self.disconnect()
        self.root.quit()
        self.root.destroy()


def main():
    """Main function"""
    print("Direct Rocket League Mod - Using Found Patterns")
    print("=" * 50)
    print("This mod uses the patterns found by the pattern updater")
    print("and applies them directly without file modifications.")
    print()
    print("Found patterns:")
    print("  car_visibility: 0x7FF708CC6B18")
    print("  title_string: 0x7FF708C04718")
    print("  player_data: 0x7FF708C5FE16")
    print()
    print("Starting GUI...")
    
    try:
        mod = DirectRLMod()
        mod.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
