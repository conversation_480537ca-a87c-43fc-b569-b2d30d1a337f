cmake_minimum_required(VERSION 3.16)
project(RocketLeagueMod)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Source files
set(SOURCES
    src/dllmain.cpp
    src/RLMod.cpp
    src/Memory.cpp
    src/CarInvisibility.cpp
    src/TitleSystem.cpp
    src/UI.cpp
    src/Config.cpp
)

# Header files
set(HEADERS
    src/pch.h
    src/RLMod.h
    src/CarInvisibility.h
    src/TitleSystem.h
    src/UI.h
    src/Config.h
)

# Create the DLL
add_library(RLMod SHARED ${SOURCES} ${HEADERS})

# Set DLL properties
set_target_properties(<PERSON>LMod PROPERTIES
    OUTPUT_NAME "RLMod"
    SUFFIX ".dll"
)

# Include directories
target_include_directories(RLMod PRIVATE
    src/
    external/imgui/
    external/json/include/
)

# Link libraries
target_link_libraries(RLMod
    d3d11.lib
    dxgi.lib
    d3dcompiler.lib
    psapi.lib
)

# Preprocessor definitions
target_compile_definitions(RLMod PRIVATE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
    _CRT_SECURE_NO_WARNINGS
)

# Compiler flags
if(MSVC)
    target_compile_options(RLMod PRIVATE
        /W3
        /permissive-
        /Zc:__cplusplus
    )
endif()

# Copy output to convenient location
add_custom_command(TARGET RLMod POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:RLMod> ${CMAKE_SOURCE_DIR}/RLMod.dll
    COMMENT "Copying DLL to project root"
)
