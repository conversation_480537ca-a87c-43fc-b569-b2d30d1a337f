#include "pch.h"
#include "CarInvisibility.h"
#include "RLMod.h"

// Global instance for hook access
extern CarInvisibility* g_pCarInvis;
extern RLMod* g_pMod;

CarInvisibility::CarInvisibility() : m_bInitialized(false), m_bCarInvisible(false),
    m_pRenderCarFunction(nullptr), m_pDrawMeshFunction(nullptr), m_pSetVisibilityFunction(nullptr),
    m_pLocalCar(nullptr), m_pCarMesh(nullptr),
    m_originalRenderCar(nullptr), m_originalDrawMesh(nullptr), m_originalSetVisibility(nullptr)
{
}

CarInvisibility::~CarInvisibility()
{
    Shutdown();
}

bool CarInvisibility::Initialize()
{
    if (m_bInitialized) return true;
    
    if (!g_pMod || !g_pMod->IsGameLoaded()) {
        return false;
    }
    
    // Find car rendering functions
    if (!FindCarRenderFunctions()) {
        return false;
    }
    
    // Install hooks
    if (m_pRenderCarFunction) {
        g_pMod->InstallHook("RenderCar", m_pRenderCarFunction, &HookedRenderCar);
        m_originalRenderCar = reinterpret_cast<RenderCarFunc>(g_pMod->GetOriginalFunction("RenderCar"));
    }
    
    if (m_pDrawMeshFunction) {
        g_pMod->InstallHook("DrawMesh", m_pDrawMeshFunction, &HookedDrawMesh);
        m_originalDrawMesh = reinterpret_cast<DrawMeshFunc>(g_pMod->GetOriginalFunction("DrawMesh"));
    }
    
    m_bInitialized = true;
    return true;
}

void CarInvisibility::Update()
{
    if (!m_bInitialized) return;
    
    // Update local car reference
    FindLocalCar();
    UpdateCarComponents();
}

void CarInvisibility::Shutdown()
{
    if (!m_bInitialized) return;
    
    // Remove hooks
    if (g_pMod) {
        g_pMod->RemoveHook("RenderCar");
        g_pMod->RemoveHook("DrawMesh");
    }
    
    // Restore visibility if invisible
    if (m_bCarInvisible) {
        SetCarInvisible(false);
    }
    
    m_bInitialized = false;
}

void CarInvisibility::ToggleCarInvisibility()
{
    SetCarInvisible(!m_bCarInvisible);
}

void CarInvisibility::SetCarInvisible(bool invisible)
{
    if (m_bCarInvisible == invisible) return;
    
    m_bCarInvisible = invisible;
    
    if (m_pLocalCar) {
        ApplyInvisibilityToAllComponents();
    }
}

bool CarInvisibility::FindCarRenderFunctions()
{
    if (!g_pMod) return false;
    
    // These patterns would need to be found through reverse engineering Rocket League
    // Using placeholder patterns for demonstration
    
    // Find car render function
    uintptr_t renderCarPattern = g_pMod->FindPattern(
        "\x48\x89\x5C\x24\x08\x48\x89\x74\x24\x10\x57\x48\x83\xEC\x20\x48\x8B\xF9",
        "xxxxxxxxxxxxxxxxxx"
    );
    
    if (renderCarPattern) {
        m_pRenderCarFunction = reinterpret_cast<void*>(renderCarPattern);
    }
    
    // Find mesh draw function
    uintptr_t drawMeshPattern = g_pMod->FindPattern(
        "\x40\x53\x48\x83\xEC\x20\x48\x8B\xD9\x48\x85\xC9\x74\x00\x48\x8B\x01",
        "xxxxxxxxxxxxx?xxx"
    );
    
    if (drawMeshPattern) {
        m_pDrawMeshFunction = reinterpret_cast<void*>(drawMeshPattern);
    }
    
    return (m_pRenderCarFunction != nullptr);
}

bool CarInvisibility::FindLocalCar()
{
    if (!g_pMod) return false;
    
    // This would require reverse engineering the game's object hierarchy
    // Placeholder implementation
    void* gameInstance = g_pMod->GetGameInstance();
    if (!gameInstance) return false;
    
    // In a real implementation, you'd traverse the game's object structure
    // to find the local player's car object
    
    return true;
}

void CarInvisibility::UpdateCarComponents()
{
    if (!m_pLocalCar) return;
    
    // Update list of car components (wheels, body, decals, etc.)
    // This would require understanding the car object structure
}

bool CarInvisibility::IsLocalCarComponent(void* component)
{
    if (!m_pLocalCar || !component) return false;
    
    // Check if the component belongs to the local player's car
    // This would require understanding the object hierarchy
    
    return false;
}

void CarInvisibility::ApplyInvisibilityToAllComponents()
{
    if (!m_pLocalCar) return;
    
    // Apply invisibility to all car components
    for (void* component : m_carComponents) {
        SetComponentVisibility(component, !m_bCarInvisible);
    }
}

void CarInvisibility::SetMeshVisibility(void* mesh, bool visible)
{
    if (!mesh) return;
    
    // Set mesh visibility flag
    // This would require understanding the mesh object structure
}

void CarInvisibility::SetComponentVisibility(void* component, bool visible)
{
    if (!component) return;
    
    // Set component visibility
    // This would involve setting render flags or visibility booleans
}

// Hook implementations
void __fastcall CarInvisibility::HookedRenderCar(void* carObject, void* context)
{
    if (g_pCarInvis && g_pCarInvis->m_bCarInvisible && g_pCarInvis->IsLocalCarComponent(carObject)) {
        // Skip rendering for local car if invisible
        return;
    }
    
    // Call original function
    if (g_pCarInvis && g_pCarInvis->m_originalRenderCar) {
        g_pCarInvis->m_originalRenderCar(carObject, context);
    }
}

void __fastcall CarInvisibility::HookedDrawMesh(void* meshObject, void* renderContext)
{
    if (g_pCarInvis && g_pCarInvis->m_bCarInvisible && g_pCarInvis->IsLocalCarComponent(meshObject)) {
        // Skip mesh drawing for local car if invisible
        return;
    }
    
    // Call original function
    if (g_pCarInvis && g_pCarInvis->m_originalDrawMesh) {
        g_pCarInvis->m_originalDrawMesh(meshObject, renderContext);
    }
}

void __fastcall CarInvisibility::HookedSetVisibility(void* object, bool visible)
{
    if (g_pCarInvis && g_pCarInvis->m_bCarInvisible && g_pCarInvis->IsLocalCarComponent(object)) {
        // Force invisible for local car components
        visible = false;
    }
    
    // Call original function
    if (g_pCarInvis && g_pCarInvis->m_originalSetVisibility) {
        g_pCarInvis->m_originalSetVisibility(object, visible);
    }
}
