@echo off
echo Rocket League Mod Injector

REM Check if RLMod.dll exists
if not exist RLMod.dll (
    echo Error: RLMod.dll not found! Please build the project first.
    pause
    exit /b 1
)

REM Check if Rocket League is running
tasklist /FI "IMAGENAME eq RocketLeague.exe" 2>NUL | find /I /N "RocketLeague.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Rocket League is running. Ready to inject.
) else (
    echo Warning: Rocket League is not running. Please start the game first.
    echo Press any key to continue anyway, or Ctrl+C to cancel.
    pause
)

REM You'll need a DLL injector tool here
REM This is just a placeholder - you'd use a tool like:
REM - Process Hacker (manual injection)
REM - Extreme Injector
REM - Your own injector
REM - Or a command-line injector

echo.
echo Manual injection required:
echo 1. Use a DLL injector tool (Process Hacker, Extreme Injector, etc.)
echo 2. Target process: RocketLeague.exe
echo 3. Inject: %CD%\RLMod.dll
echo 4. Press INSERT in-game to open the mod UI
echo.
echo Hotkeys:
echo - INSERT: Toggle mod UI
echo - F1: Toggle car invisibility
echo - F2: Cycle titles
echo.

pause
