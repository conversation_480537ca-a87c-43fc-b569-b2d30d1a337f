"""
GUI Interface - Modern user interface for the Rocket League mod
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import customtkinter as ctk
from typing import Optional, Callable, Dict, Any
import threading
import time


class ModernGUI:
    """Modern GUI interface using CustomTkinter"""
    
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Main window
        self.root = ctk.CTk()
        self.root.title("🚀 Rocket League Mod")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Callbacks for mod functions
        self.callbacks: Dict[str, Callable] = {}
        
        # Status variables
        self.game_status = tk.StringVar(value="❌ Not Connected")
        self.car_invisible = tk.BooleanVar(value=False)
        self.current_title = tk.StringVar(value="No Title Selected")
        
        # GUI state
        self.is_running = False
        self.status_thread: Optional[threading.Thread] = None
        
        # Create GUI elements
        self._create_widgets()
        self._setup_layout()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def _create_widgets(self):
        """Create all GUI widgets"""
        
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        
        # Header
        self.header_frame = ctk.CTkFrame(self.main_frame)
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="🚀 Rocket League Mod",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.status_label = ctk.CTkLabel(
            self.header_frame,
            textvariable=self.game_status,
            font=ctk.CTkFont(size=14)
        )
        
        # Connection section
        self.connection_frame = ctk.CTkFrame(self.main_frame)
        self.connection_title = ctk.CTkLabel(
            self.connection_frame,
            text="🔌 Game Connection",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.connect_button = ctk.CTkButton(
            self.connection_frame,
            text="Connect to Rocket League",
            command=self._on_connect_clicked
        )
        self.disconnect_button = ctk.CTkButton(
            self.connection_frame,
            text="Disconnect",
            command=self._on_disconnect_clicked,
            state="disabled"
        )
        
        # Car invisibility section
        self.car_frame = ctk.CTkFrame(self.main_frame)
        self.car_title = ctk.CTkLabel(
            self.car_frame,
            text="👻 Car Invisibility",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.car_toggle = ctk.CTkSwitch(
            self.car_frame,
            text="Make Car Invisible",
            variable=self.car_invisible,
            command=self._on_car_toggle
        )
        self.car_status = ctk.CTkLabel(
            self.car_frame,
            text="Status: Visible",
            font=ctk.CTkFont(size=12)
        )
        
        # Title system section
        self.title_frame = ctk.CTkFrame(self.main_frame)
        self.title_section_label = ctk.CTkLabel(
            self.title_frame,
            text="🏆 Title System",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        # Current title display
        self.current_title_label = ctk.CTkLabel(
            self.title_frame,
            textvariable=self.current_title,
            font=ctk.CTkFont(size=14)
        )
        
        # Title selection
        self.title_combobox = ctk.CTkComboBox(
            self.title_frame,
            values=["Loading titles..."],
            command=self._on_title_selected
        )
        
        # Title buttons
        self.title_buttons_frame = ctk.CTkFrame(self.title_frame)
        self.cycle_title_button = ctk.CTkButton(
            self.title_buttons_frame,
            text="Cycle Title (F2)",
            command=self._on_cycle_title
        )
        self.add_custom_button = ctk.CTkButton(
            self.title_buttons_frame,
            text="Add Custom Title",
            command=self._on_add_custom_title
        )
        
        # Custom title entry
        self.custom_title_frame = ctk.CTkFrame(self.title_frame)
        self.custom_title_entry = ctk.CTkEntry(
            self.custom_title_frame,
            placeholder_text="Enter custom title..."
        )
        self.add_title_button = ctk.CTkButton(
            self.custom_title_frame,
            text="Add",
            command=self._on_add_title_clicked
        )
        
        # Hotkeys section
        self.hotkeys_frame = ctk.CTkFrame(self.main_frame)
        self.hotkeys_title = ctk.CTkLabel(
            self.hotkeys_frame,
            text="⌨️ Hotkeys",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.hotkeys_text = ctk.CTkLabel(
            self.hotkeys_frame,
            text="F1: Toggle Car Invisibility\nF2: Cycle Titles\nF3: Toggle GUI",
            font=ctk.CTkFont(size=12),
            justify="left"
        )
        
        # Log section
        self.log_frame = ctk.CTkFrame(self.main_frame)
        self.log_title = ctk.CTkLabel(
            self.log_frame,
            text="📝 Activity Log",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.log_text = ctk.CTkTextbox(
            self.log_frame,
            height=150,
            font=ctk.CTkFont(size=10)
        )
        self.clear_log_button = ctk.CTkButton(
            self.log_frame,
            text="Clear Log",
            command=self._clear_log
        )
        
        # Settings section
        self.settings_frame = ctk.CTkFrame(self.main_frame)
        self.settings_title = ctk.CTkLabel(
            self.settings_frame,
            text="⚙️ Settings",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.auto_connect = ctk.CTkCheckBox(
            self.settings_frame,
            text="Auto-connect on startup"
        )
        self.minimize_to_tray = ctk.CTkCheckBox(
            self.settings_frame,
            text="Minimize to system tray"
        )
    
    def _setup_layout(self):
        """Setup the layout of all widgets"""
        
        # Main frame
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header
        self.header_frame.pack(fill="x", pady=(0, 10))
        self.title_label.pack(pady=5)
        self.status_label.pack(pady=5)
        
        # Connection section
        self.connection_frame.pack(fill="x", pady=(0, 10))
        self.connection_title.pack(pady=5)
        
        connection_buttons = ctk.CTkFrame(self.connection_frame)
        connection_buttons.pack(pady=5)
        self.connect_button.pack(side="left", padx=5)
        self.disconnect_button.pack(side="left", padx=5)
        
        # Car invisibility section
        self.car_frame.pack(fill="x", pady=(0, 10))
        self.car_title.pack(pady=5)
        self.car_toggle.pack(pady=5)
        self.car_status.pack(pady=5)
        
        # Title system section
        self.title_frame.pack(fill="x", pady=(0, 10))
        self.title_section_label.pack(pady=5)
        self.current_title_label.pack(pady=5)
        self.title_combobox.pack(pady=5, padx=20, fill="x")
        
        # Title buttons
        self.title_buttons_frame.pack(pady=5)
        self.cycle_title_button.pack(side="left", padx=5)
        self.add_custom_button.pack(side="left", padx=5)
        
        # Custom title entry
        self.custom_title_frame.pack(fill="x", pady=5, padx=20)
        self.custom_title_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        self.add_title_button.pack(side="right")
        
        # Hotkeys section
        self.hotkeys_frame.pack(fill="x", pady=(0, 10))
        self.hotkeys_title.pack(pady=5)
        self.hotkeys_text.pack(pady=5)
        
        # Log section
        self.log_frame.pack(fill="both", expand=True, pady=(0, 10))
        self.log_title.pack(pady=5)
        self.log_text.pack(fill="both", expand=True, padx=10, pady=5)
        self.clear_log_button.pack(pady=5)
        
        # Settings section
        self.settings_frame.pack(fill="x")
        self.settings_title.pack(pady=5)
        self.auto_connect.pack(pady=2)
        self.minimize_to_tray.pack(pady=2)
    
    def register_callback(self, event: str, callback: Callable):
        """Register callback for GUI events"""
        self.callbacks[event] = callback
    
    def _trigger_callback(self, event: str, *args, **kwargs):
        """Trigger registered callback"""
        if event in self.callbacks:
            try:
                return self.callbacks[event](*args, **kwargs)
            except Exception as e:
                self.log_message(f"❌ Callback error for {event}: {e}")
                return False
        return False
    
    def _on_connect_clicked(self):
        """Handle connect button click"""
        self.log_message("🔌 Attempting to connect to Rocket League...")
        success = self._trigger_callback('connect_to_game')
        
        if success:
            self.connect_button.configure(state="disabled")
            self.disconnect_button.configure(state="normal")
            self.game_status.set("✅ Connected")
            self.log_message("✅ Successfully connected to Rocket League!")
        else:
            self.log_message("❌ Failed to connect to Rocket League")
    
    def _on_disconnect_clicked(self):
        """Handle disconnect button click"""
        self.log_message("🔌 Disconnecting from Rocket League...")
        self._trigger_callback('disconnect_from_game')
        
        self.connect_button.configure(state="normal")
        self.disconnect_button.configure(state="disabled")
        self.game_status.set("❌ Not Connected")
        self.log_message("🔌 Disconnected from Rocket League")
    
    def _on_car_toggle(self):
        """Handle car invisibility toggle"""
        is_invisible = self.car_invisible.get()
        
        if is_invisible:
            self.log_message("👻 Making car invisible...")
            success = self._trigger_callback('make_car_invisible')
            if success:
                self.car_status.configure(text="Status: Invisible")
                self.log_message("✅ Car is now invisible!")
            else:
                self.car_invisible.set(False)
                self.log_message("❌ Failed to make car invisible")
        else:
            self.log_message("👁️ Making car visible...")
            success = self._trigger_callback('make_car_visible')
            if success:
                self.car_status.configure(text="Status: Visible")
                self.log_message("✅ Car is now visible!")
            else:
                self.car_invisible.set(True)
                self.log_message("❌ Failed to make car visible")
    
    def _on_title_selected(self, title_name: str):
        """Handle title selection from combobox"""
        self.log_message(f"🏆 Setting title to: {title_name}")
        success = self._trigger_callback('set_title_by_name', title_name)
        
        if success:
            self.current_title.set(f"Current: {title_name}")
            self.log_message(f"✅ Title set to: {title_name}")
        else:
            self.log_message(f"❌ Failed to set title: {title_name}")
    
    def _on_cycle_title(self):
        """Handle cycle title button"""
        self.log_message("🔄 Cycling to next title...")
        success = self._trigger_callback('cycle_title')
        
        if success:
            # Get current title from callback
            current_title = self._trigger_callback('get_current_title')
            if current_title:
                self.current_title.set(f"Current: {current_title}")
                self.log_message(f"✅ Cycled to: {current_title}")
        else:
            self.log_message("❌ Failed to cycle title")
    
    def _on_add_custom_title(self):
        """Handle add custom title button"""
        # Show dialog for custom title input
        dialog = ctk.CTkInputDialog(
            text="Enter custom title:",
            title="Add Custom Title"
        )
        title_text = dialog.get_input()
        
        if title_text:
            self.log_message(f"➕ Adding custom title: {title_text}")
            success = self._trigger_callback('add_custom_title', title_text)
            
            if success:
                self.log_message(f"✅ Added custom title: {title_text}")
                self.update_title_list()
            else:
                self.log_message(f"❌ Failed to add custom title: {title_text}")
    
    def _on_add_title_clicked(self):
        """Handle add title button from entry"""
        title_text = self.custom_title_entry.get().strip()
        
        if not title_text:
            messagebox.showwarning("Warning", "Please enter a title name")
            return
        
        self.log_message(f"➕ Adding custom title: {title_text}")
        success = self._trigger_callback('add_custom_title', title_text)
        
        if success:
            self.custom_title_entry.delete(0, tk.END)
            self.log_message(f"✅ Added custom title: {title_text}")
            self.update_title_list()
        else:
            self.log_message(f"❌ Failed to add custom title: {title_text}")
    
    def _clear_log(self):
        """Clear the activity log"""
        self.log_text.delete("1.0", tk.END)
    
    def log_message(self, message: str):
        """Add message to activity log"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
    
    def update_title_list(self):
        """Update the title combobox with available titles"""
        titles = self._trigger_callback('get_available_titles')
        if titles:
            title_names = [title.display_text for title in titles]
            self.title_combobox.configure(values=title_names)
    
    def update_status(self, status_data: Dict[str, Any]):
        """Update GUI with current status"""
        # Update game connection status
        if status_data.get('connected', False):
            self.game_status.set("✅ Connected")
            self.connect_button.configure(state="disabled")
            self.disconnect_button.configure(state="normal")
        else:
            self.game_status.set("❌ Not Connected")
            self.connect_button.configure(state="normal")
            self.disconnect_button.configure(state="disabled")
        
        # Update car invisibility status
        car_invisible = status_data.get('car_invisible', False)
        self.car_invisible.set(car_invisible)
        self.car_status.configure(
            text=f"Status: {'Invisible' if car_invisible else 'Visible'}"
        )
        
        # Update current title
        current_title = status_data.get('current_title', 'No Title')
        self.current_title.set(f"Current: {current_title}")
    
    def start_status_updates(self):
        """Start background status updates"""
        self.is_running = True
        self.status_thread = threading.Thread(target=self._status_update_loop, daemon=True)
        self.status_thread.start()
    
    def _status_update_loop(self):
        """Background loop for status updates"""
        while self.is_running:
            try:
                status = self._trigger_callback('get_status')
                if status:
                    self.root.after(0, lambda: self.update_status(status))
                time.sleep(1.0)
            except Exception as e:
                print(f"Status update error: {e}")
                time.sleep(1.0)
    
    def on_closing(self):
        """Handle window closing"""
        self.is_running = False
        
        # Trigger cleanup callback
        self._trigger_callback('cleanup')
        
        # Close the window
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Start the GUI main loop"""
        self.start_status_updates()
        self.root.mainloop()
    
    def hide(self):
        """Hide the GUI window"""
        self.root.withdraw()
    
    def show(self):
        """Show the GUI window"""
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()
