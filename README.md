# Rocket League Mod - Car Invisibility & Custom Titles

A DLL injection mod for Rocket League that provides:
- Toggleable car invisibility
- Custom server-side titles
- User-friendly interface

## Features

### Car Invisibility
- Toggle your car's visibility on/off
- Affects rendering but maintains collision
- Server-side compatible

### Custom Titles
- Use any title server-side
- Bypass normal title restrictions
- Appears to other players

### User Interface
- Easy-to-use toggle interface
- Hotkey support
- Settings persistence

## Installation

1. Compile the DLL using Visual Studio
2. Use a DLL injector to inject `RLMod.dll` into RocketLeague.exe
3. Press INSERT key to toggle the UI
4. Configure your settings

## Usage

- **INSERT**: Toggle mod UI
- **F1**: Toggle car invisibility
- **F2**: Cycle through custom titles

## Technical Details

This mod works by:
- Hooking into Rocket League's rendering pipeline
- Modifying mesh visibility flags
- Intercepting title display functions
- Using memory patching for server-side effects

## Disclaimer

This mod is for educational purposes. Use at your own risk.
