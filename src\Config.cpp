#include "pch.h"
#include "Config.h"

Config::Config() : m_bLoaded(false)
{
    m_configPath = GetConfigPath();
}

Config::~Config()
{
    if (m_bLoaded) {
        Save();
    }
}

bool Config::Load()
{
    try {
        std::ifstream configFile(m_configPath);
        if (!configFile.is_open()) {
            // Create default config if file doesn't exist
            m_config = ModConfig();
            Save();
            m_bLoaded = true;
            return true;
        }
        
        json configJson;
        configFile >> configJson;
        configFile.close();
        
        JsonToConfig(configJson);
        m_bLoaded = true;
        return true;
    }
    catch (const std::exception& e) {
        // If loading fails, use default config
        m_config = ModConfig();
        m_bLoaded = true;
        return false;
    }
}

bool Config::Save()
{
    try {
        json configJson = ConfigToJson();
        
        std::ofstream configFile(m_configPath);
        if (!configFile.is_open()) {
            return false;
        }
        
        configFile << configJson.dump(4);
        configFile.close();
        
        return true;
    }
    catch (const std::exception& e) {
        return false;
    }
}

void Config::SetCarInvisibilityEnabled(bool enabled)
{
    m_config.carInvisibilityEnabled = enabled;
    if (m_bLoaded) {
        Save();
    }
}

void Config::SetSelectedTitleIndex(int index)
{
    m_config.selectedTitleIndex = index;
    if (m_bLoaded) {
        Save();
    }
}

void Config::SetCustomTitle(const std::string& title)
{
    m_config.customTitle = title;
    if (m_bLoaded) {
        Save();
    }
}

void Config::AddCustomTitle(const std::string& title)
{
    // Check if title already exists
    for (const auto& existingTitle : m_config.customTitles) {
        if (existingTitle == title) {
            return;
        }
    }
    
    m_config.customTitles.push_back(title);
    if (m_bLoaded) {
        Save();
    }
}

void Config::RemoveCustomTitle(const std::string& title)
{
    auto it = std::find(m_config.customTitles.begin(), m_config.customTitles.end(), title);
    if (it != m_config.customTitles.end()) {
        m_config.customTitles.erase(it);
        if (m_bLoaded) {
            Save();
        }
    }
}

json Config::ConfigToJson() const
{
    json j;
    
    // Car invisibility settings
    j["car_invisibility"]["enabled"] = m_config.carInvisibilityEnabled;
    j["car_invisibility"]["remember_state"] = m_config.rememberCarInvisibilityState;
    
    // Title settings
    j["titles"]["selected_index"] = m_config.selectedTitleIndex;
    j["titles"]["custom_title"] = m_config.customTitle;
    j["titles"]["custom_titles"] = m_config.customTitles;
    j["titles"]["remember_selection"] = m_config.rememberTitleSelection;
    
    // UI settings
    j["ui"]["show_on_startup"] = m_config.showUIOnStartup;
    j["ui"]["scale"] = m_config.uiScale;
    j["ui"]["main_window"]["pos"]["x"] = m_config.mainWindowPos.x;
    j["ui"]["main_window"]["pos"]["y"] = m_config.mainWindowPos.y;
    j["ui"]["main_window"]["size"]["width"] = m_config.mainWindowSize.x;
    j["ui"]["main_window"]["size"]["height"] = m_config.mainWindowSize.y;
    
    // Hotkey settings
    j["hotkeys"]["toggle_ui"] = m_config.toggleUIKey;
    j["hotkeys"]["toggle_car_invisibility"] = m_config.toggleCarInvisibilityKey;
    j["hotkeys"]["cycle_title"] = m_config.cycleTitleKey;
    
    // Advanced settings
    j["advanced"]["enable_logging"] = m_config.enableLogging;
    j["advanced"]["log_level"] = m_config.logLevel;
    j["advanced"]["auto_inject"] = m_config.autoInjectOnGameStart;
    
    return j;
}

void Config::JsonToConfig(const json& j)
{
    try {
        // Car invisibility settings
        if (j.contains("car_invisibility")) {
            auto& carInvis = j["car_invisibility"];
            if (carInvis.contains("enabled")) {
                m_config.carInvisibilityEnabled = carInvis["enabled"];
            }
            if (carInvis.contains("remember_state")) {
                m_config.rememberCarInvisibilityState = carInvis["remember_state"];
            }
        }
        
        // Title settings
        if (j.contains("titles")) {
            auto& titles = j["titles"];
            if (titles.contains("selected_index")) {
                m_config.selectedTitleIndex = titles["selected_index"];
            }
            if (titles.contains("custom_title")) {
                m_config.customTitle = titles["custom_title"];
            }
            if (titles.contains("custom_titles")) {
                m_config.customTitles = titles["custom_titles"];
            }
            if (titles.contains("remember_selection")) {
                m_config.rememberTitleSelection = titles["remember_selection"];
            }
        }
        
        // UI settings
        if (j.contains("ui")) {
            auto& ui = j["ui"];
            if (ui.contains("show_on_startup")) {
                m_config.showUIOnStartup = ui["show_on_startup"];
            }
            if (ui.contains("scale")) {
                m_config.uiScale = ui["scale"];
            }
            if (ui.contains("main_window")) {
                auto& mainWindow = ui["main_window"];
                if (mainWindow.contains("pos")) {
                    m_config.mainWindowPos.x = mainWindow["pos"]["x"];
                    m_config.mainWindowPos.y = mainWindow["pos"]["y"];
                }
                if (mainWindow.contains("size")) {
                    m_config.mainWindowSize.x = mainWindow["size"]["width"];
                    m_config.mainWindowSize.y = mainWindow["size"]["height"];
                }
            }
        }
        
        // Hotkey settings
        if (j.contains("hotkeys")) {
            auto& hotkeys = j["hotkeys"];
            if (hotkeys.contains("toggle_ui")) {
                m_config.toggleUIKey = hotkeys["toggle_ui"];
            }
            if (hotkeys.contains("toggle_car_invisibility")) {
                m_config.toggleCarInvisibilityKey = hotkeys["toggle_car_invisibility"];
            }
            if (hotkeys.contains("cycle_title")) {
                m_config.cycleTitleKey = hotkeys["cycle_title"];
            }
        }
        
        // Advanced settings
        if (j.contains("advanced")) {
            auto& advanced = j["advanced"];
            if (advanced.contains("enable_logging")) {
                m_config.enableLogging = advanced["enable_logging"];
            }
            if (advanced.contains("log_level")) {
                m_config.logLevel = advanced["log_level"];
            }
            if (advanced.contains("auto_inject")) {
                m_config.autoInjectOnGameStart = advanced["auto_inject"];
            }
        }
    }
    catch (const std::exception& e) {
        // If parsing fails, keep current config
    }
}

std::string Config::GetConfigPath() const
{
    char path[MAX_PATH];
    GetModuleFileNameA(nullptr, path, MAX_PATH);
    std::string exePath(path);
    size_t lastSlash = exePath.find_last_of("\\/");
    if (lastSlash != std::string::npos) {
        exePath = exePath.substr(0, lastSlash + 1);
    }
    return exePath + "rlmod_config.json";
}
