"""
Game Scanner - Detects and monitors Rocket League process
"""

import psutil
import time
import threading
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass


@dataclass
class GameInfo:
    """Information about the detected game process"""
    process_id: int
    process_name: str
    executable_path: str
    memory_usage: int
    is_running: bool = True


class GameScanner:
    """Monitors Rocket League process and provides game state information"""
    
    def __init__(self):
        self.game_info: Optional[GameInfo] = None
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.callbacks: Dict[str, Callable] = {}
        
        # Game detection patterns
        self.game_patterns = [
            "RocketLeague.exe",
            "RocketLeague",
            "rl.exe",
            "rocketleague.exe"
        ]
    
    def start_monitoring(self, interval: float = 2.0):
        """Start monitoring for Rocket League process"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print("🔍 Started monitoring for Rocket League...")
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        print("⏹️ Stopped monitoring")
    
    def _monitor_loop(self, interval: float):
        """Main monitoring loop"""
        last_state = None
        
        while self.is_monitoring:
            try:
                current_game = self._detect_game()
                
                # Check for state changes
                if current_game and not last_state:
                    # Game started
                    self.game_info = current_game
                    self._trigger_callback('game_started', current_game)
                    print(f"🎮 Rocket League detected! PID: {current_game.process_id}")
                
                elif not current_game and last_state:
                    # Game stopped
                    self._trigger_callback('game_stopped', last_state)
                    print("🔴 Rocket League closed")
                    self.game_info = None
                
                elif current_game and last_state:
                    # Game running - update info
                    self.game_info = current_game
                    self._trigger_callback('game_update', current_game)
                
                last_state = current_game
                time.sleep(interval)
                
            except Exception as e:
                print(f"❌ Monitor error: {e}")
                time.sleep(interval)
    
    def _detect_game(self) -> Optional[GameInfo]:
        """Detect if Rocket League is running"""
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'memory_info']):
            try:
                proc_info = proc.info
                proc_name = proc_info['name']
                
                # Check if this matches our game patterns
                if any(pattern.lower() in proc_name.lower() for pattern in self.game_patterns):
                    return GameInfo(
                        process_id=proc_info['pid'],
                        process_name=proc_name,
                        executable_path=proc_info['exe'] or "Unknown",
                        memory_usage=proc_info['memory_info'].rss if proc_info['memory_info'] else 0
                    )
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        return None
    
    def is_game_running(self) -> bool:
        """Check if game is currently running"""
        return self.game_info is not None and self.game_info.is_running
    
    def get_game_info(self) -> Optional[GameInfo]:
        """Get current game information"""
        return self.game_info
    
    def wait_for_game(self, timeout: float = 30.0) -> bool:
        """Wait for game to start (blocking)"""
        print(f"⏳ Waiting for Rocket League (timeout: {timeout}s)...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self._detect_game():
                return True
            time.sleep(0.5)
        
        print("⏰ Timeout waiting for Rocket League")
        return False
    
    def register_callback(self, event: str, callback: Callable):
        """Register callback for game events
        
        Events:
        - 'game_started': Called when game starts
        - 'game_stopped': Called when game stops  
        - 'game_update': Called periodically while game runs
        """
        self.callbacks[event] = callback
    
    def _trigger_callback(self, event: str, data: Any):
        """Trigger registered callback"""
        if event in self.callbacks:
            try:
                self.callbacks[event](data)
            except Exception as e:
                print(f"❌ Callback error for {event}: {e}")
    
    def get_process_stats(self) -> Optional[Dict[str, Any]]:
        """Get detailed process statistics"""
        if not self.game_info:
            return None
        
        try:
            proc = psutil.Process(self.game_info.process_id)
            
            return {
                'cpu_percent': proc.cpu_percent(),
                'memory_percent': proc.memory_percent(),
                'memory_info': proc.memory_info()._asdict(),
                'num_threads': proc.num_threads(),
                'create_time': proc.create_time(),
                'status': proc.status(),
                'connections': len(proc.connections()) if hasattr(proc, 'connections') else 0
            }
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return None
    
    def is_game_in_foreground(self) -> bool:
        """Check if Rocket League window is in foreground"""
        try:
            import ctypes
            from ctypes import wintypes
            
            # Get foreground window
            user32 = ctypes.windll.user32
            foreground_window = user32.GetForegroundWindow()
            
            if not foreground_window:
                return False
            
            # Get window process ID
            process_id = wintypes.DWORD()
            user32.GetWindowThreadProcessId(foreground_window, ctypes.byref(process_id))
            
            return (self.game_info and 
                    process_id.value == self.game_info.process_id)
            
        except Exception:
            return False
    
    def get_window_title(self) -> Optional[str]:
        """Get Rocket League window title"""
        if not self.game_info:
            return None
        
        try:
            import ctypes
            from ctypes import wintypes
            
            # Find window by process ID
            def enum_windows_callback(hwnd, windows):
                process_id = wintypes.DWORD()
                ctypes.windll.user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))
                
                if process_id.value == self.game_info.process_id:
                    # Get window title
                    length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        ctypes.windll.user32.GetWindowTextW(hwnd, buffer, length + 1)
                        windows.append(buffer.value)
                
                return True
            
            windows = []
            ctypes.windll.user32.EnumWindows(
                ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.POINTER(ctypes.c_int), ctypes.POINTER(ctypes.c_int))(enum_windows_callback),
                ctypes.byref(ctypes.c_int())
            )
            
            # Return the first non-empty window title
            for title in windows:
                if title.strip():
                    return title
            
            return None
            
        except Exception as e:
            print(f"Error getting window title: {e}")
            return None
