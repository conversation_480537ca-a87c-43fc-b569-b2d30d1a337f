# Installation Guide

## Prerequisites

1. **Visual Studio 2022** with C++ development tools
2. **CMake** (3.16 or later)
3. **Git** (for downloading dependencies)

## Building the Mod

### Step 1: Setup Dependencies

Run the dependency setup script:
```bash
cd external
setup_dependencies.bat
```

Or manually download:
- **ImGui**: Download from https://github.com/ocornut/imgui and extract to `external/imgui/`
- **nlohmann/json**: Download `json.hpp` and place in `external/json/include/nlohmann/`

### Step 2: Build the Project

Run the build script:
```bash
build.bat
```

Or manually build:
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### Step 3: Injection

1. Start Rocket League
2. Run `inject.bat` for instructions
3. Use a DLL injector to inject `RLMod.dll` into `RocketLeague.exe`

## Recommended DLL Injectors

- **Process Hacker** (Free, GUI-based)
- **Extreme Injector** (Free, simple)
- **Cheat Engine** (Free, advanced features)

## Usage

Once injected:
- Press **INSERT** to toggle the mod UI
- Press **F1** to toggle car invisibility
- Press **F2** to cycle through titles

## Troubleshooting

### Build Issues
- Ensure Visual Studio 2022 is installed with C++ tools
- Check that dependencies are in the correct folders
- Try cleaning and rebuilding: `rmdir /s build` then run `build.bat`

### Injection Issues
- Make sure Rocket League is running
- Run injector as Administrator
- Verify the DLL path is correct
- Check Windows Defender/antivirus isn't blocking the DLL

### Game Crashes
- The mod uses placeholder memory patterns that need to be updated for your game version
- Reverse engineering is required to find correct function addresses
- Consider using a different injection method

## Important Notes

⚠️ **This mod is for educational purposes only**
⚠️ **Use at your own risk - may result in game bans**
⚠️ **Memory patterns need to be updated for each game version**

## Next Steps

To make this mod functional, you'll need to:
1. Reverse engineer Rocket League to find actual function addresses
2. Update memory patterns in the source code
3. Test with the specific version of Rocket League you're targeting
