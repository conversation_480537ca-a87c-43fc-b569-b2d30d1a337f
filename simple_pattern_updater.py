#!/usr/bin/env python3
"""
Simple Pattern Updater - No Unicode Issues
Automatically finds memory patterns and updates mod files
"""

import pymem
import psutil
import struct
import re
import os
import shutil
from typing import Dict, List, Optional, Tuple
import time


class SimplePatternUpdater:
    """Simple pattern updater without Unicode characters"""
    
    def __init__(self):
        self.process = None
        self.base_address = None
        self.module_size = None
        self.found_patterns = {}
        
    def connect_to_game(self):
        """Connect to Rocket League"""
        print("[*] Searching for Rocket League...")
        
        try:
            # Find Rocket League process
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name']
                if any(name in proc_name.lower() for name in ['rocket', 'rl.exe']):
                    break
            else:
                print("[!] Rocket League not found!")
                print("    Please start Rocket League and try again.")
                return False
            
            # Connect to process
            self.process = pymem.Pymem(proc_name)
            
            # Get module info
            modules = list(self.process.list_modules())
            main_module = modules[0]
            self.base_address = main_module.lpBaseOfDll
            self.module_size = main_module.SizeOfImage
            
            print(f"[+] Connected to {proc_name}")
            print(f"[+] Base: 0x{self.base_address:X}")
            print(f"[+] Size: 0x{self.module_size:X}")
            return True
            
        except Exception as e:
            print(f"[!] Connection failed: {e}")
            return False
    
    def find_patterns(self):
        """Find memory patterns"""
        print("\n[*] Scanning for memory patterns...")
        
        # Simple patterns that are likely to work
        patterns = {
            'car_visibility': [
                (b'\x88\x44\x24\x20', 'xxxx'),
                (b'\x88\x47\x00', 'xx?'),
                (b'\x80\x7F\x00\x00', 'xx??'),
            ],
            'title_string': [
                (b'\x48\x8D\x15\x00\x00\x00\x00', 'xxx????'),
                (b'\x48\x8B\x05\x00\x00\x00\x00', 'xxx????'),
            ],
            'player_data': [
                (b'\x48\x8B\x0D\x00\x00\x00\x00', 'xxx????'),
                (b'\x48\x8B\x80\x00\x00\x00\x00', 'xxx????'),
            ]
        }
        
        found_count = 0
        
        for pattern_name, pattern_list in patterns.items():
            print(f"  [*] Searching for {pattern_name}...")
            
            for pattern, mask in pattern_list:
                addresses = self._scan_pattern(pattern, mask)
                if addresses:
                    self.found_patterns[pattern_name] = addresses[0]
                    print(f"      [+] Found at 0x{addresses[0]:X}")
                    found_count += 1
                    break
            else:
                print(f"      [-] Not found")
        
        print(f"\n[*] Results: {found_count}/{len(patterns)} patterns found")
        
        # Need at least 2 patterns to proceed
        if found_count >= 2:
            print("[+] Sufficient patterns found!")
            return True
        else:
            print("[!] Insufficient patterns found")
            return False
    
    def _scan_pattern(self, pattern, mask):
        """Scan for pattern"""
        addresses = []
        chunk_size = 0x10000
        
        try:
            for offset in range(0, self.module_size, chunk_size):
                if offset + len(pattern) > self.module_size:
                    break
                
                try:
                    chunk = self.process.read_bytes(
                        self.base_address + offset,
                        min(chunk_size, self.module_size - offset)
                    )
                    
                    for i in range(len(chunk) - len(pattern)):
                        match = True
                        for j in range(len(pattern)):
                            if mask[j] != '?' and chunk[i + j] != pattern[j]:
                                match = False
                                break
                        
                        if match:
                            addr = self.base_address + offset + i
                            addresses.append(addr)
                            
                            if len(addresses) >= 5:
                                return addresses
                                
                except Exception:
                    continue
                    
        except Exception:
            pass
        
        return addresses
    
    def update_files(self):
        """Update mod files with found patterns"""
        print("\n[*] Updating mod files...")
        
        try:
            # Backup files
            self._backup_files()
            
            # Update car invisibility
            self._update_car_file()
            
            # Update title system
            self._update_title_file()
            
            print("[+] Files updated successfully!")
            return True
            
        except Exception as e:
            print(f"[!] Error updating files: {e}")
            return False
    
    def _backup_files(self):
        """Backup original files"""
        backup_dir = 'backup_original'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        files = ['car_invisibility.py', 'title_system.py']
        for filename in files:
            if os.path.exists(filename):
                shutil.copy2(filename, os.path.join(backup_dir, filename))
        
        print("  [+] Files backed up")
    
    def _update_car_file(self):
        """Update car invisibility file"""
        if 'car_visibility' not in self.found_patterns:
            return
        
        try:
            with open('car_invisibility.py', 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            addr = self.found_patterns['car_visibility']
            
            # Replace the make_invisible method
            old_method = r'def make_invisible\(self\) -> bool:.*?return True'
            new_method = f'''def make_invisible(self) -> bool:
        """Make the car invisible"""
        if not self.memory.is_connected:
            print("[!] Not connected to game")
            return False
        
        if self.is_invisible:
            print("[*] Car is already invisible")
            return True
        
        print("[*] Making car invisible...")
        
        try:
            # Write to found memory address
            if self.memory.write_memory(0x{addr:X}, b'\\x00'):
                self.is_invisible = True
                print("[+] Car is now invisible!")
                return True
            else:
                print("[!] Failed to write memory")
                return False
                
        except Exception as e:
            print(f"[!] Error: {{e}}")
            return False'''
            
            content = re.sub(old_method, new_method, content, flags=re.DOTALL)
            
            # Replace the make_visible method
            old_visible = r'def make_visible\(self\) -> bool:.*?return True'
            new_visible = f'''def make_visible(self) -> bool:
        """Make the car visible again"""
        if not self.memory.is_connected:
            print("[!] Not connected to game")
            return False
        
        if not self.is_invisible:
            print("[*] Car is already visible")
            return True
        
        print("[*] Making car visible...")
        
        try:
            # Write to found memory address
            if self.memory.write_memory(0x{addr:X}, b'\\x01'):
                self.is_invisible = False
                print("[+] Car is now visible!")
                return True
            else:
                print("[!] Failed to write memory")
                return False
                
        except Exception as e:
            print(f"[!] Error: {{e}}")
            return False'''
            
            content = re.sub(old_visible, new_visible, content, flags=re.DOTALL)
            
            with open('car_invisibility.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  [+] Updated car_invisibility.py")
            
        except Exception as e:
            print(f"  [!] Error updating car file: {e}")
    
    def _update_title_file(self):
        """Update title system file"""
        if 'title_string' not in self.found_patterns:
            return
        
        try:
            with open('title_system.py', 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            addr = self.found_patterns['title_string']
            
            # Replace the _apply_title method
            old_apply = r'def _apply_title\(self\) -> bool:.*?return True'
            new_apply = f'''def _apply_title(self) -> bool:
        """Apply the currently selected title"""
        current_title = self.get_current_title()
        if not current_title:
            return False
        
        try:
            # Write title to found memory address
            title_bytes = current_title.display_text.encode('utf-8') + b'\\x00'
            if self.memory.write_memory(0x{addr:X}, title_bytes):
                print(f"[+] Applied title: '{{current_title.display_text}}'")
                return True
            else:
                print(f"[!] Failed to apply title: '{{current_title.display_text}}'")
                return False
                
        except Exception as e:
            print(f"[!] Error applying title: {{e}}")
            return False'''
            
            content = re.sub(old_apply, new_apply, content, flags=re.DOTALL)
            
            with open('title_system.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  [+] Updated title_system.py")
            
        except Exception as e:
            print(f"  [!] Error updating title file: {e}")
    
    def run(self):
        """Main execution"""
        print("Auto Pattern Updater for Rocket League Mod")
        print("=" * 45)
        
        # Connect to game
        if not self.connect_to_game():
            print("\n[!] FAILED: Could not connect to Rocket League")
            return False
        
        # Find patterns
        if not self.find_patterns():
            print("\n[!] FAILED: Insufficient patterns found")
            return False
        
        # Update files
        if not self.update_files():
            print("\n[!] FAILED: Could not update files")
            return False
        
        # Success
        print("\n" + "=" * 45)
        print("[+] SUCCESS! Your mod has been updated!")
        print("=" * 45)
        print(f"[*] Found and applied {len(self.found_patterns)} patterns:")
        
        for name, addr in self.found_patterns.items():
            print(f"  [+] {name}: 0x{addr:X}")
        
        print("\n[*] Your mod is now functional!")
        print("    Run: python rocket_league_mod.py")
        print("\n[*] Hotkeys:")
        print("    F1: Toggle car invisibility (REAL)")
        print("    F2: Cycle titles (REAL)")
        print("    F3: Toggle GUI")
        
        return True


def main():
    """Main function"""
    updater = SimplePatternUpdater()
    
    try:
        success = updater.run()
        
        if not success:
            print("\n[*] Tips:")
            print("    - Make sure Rocket League is running")
            print("    - Try restarting the game")
            print("    - Check if you have the latest version")
            
    except Exception as e:
        print(f"\n[!] Unexpected error: {e}")


if __name__ == "__main__":
    main()
