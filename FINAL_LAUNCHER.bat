@echo off
title Rocket League Mod - Final Launcher

echo ==========================================
echo   Rocket League Mod - Final Launcher
echo ==========================================
echo.
echo This launcher will:
echo   1. Find memory patterns automatically
echo   2. Update your mod files if patterns found
echo   3. Launch the fully functional mod
echo   4. Exit safely if patterns not found
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [!] Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Check if Rocket League is running
echo [*] Checking for Rocket League...
tasklist /FI "IMAGENAME eq RocketLeague.exe" 2>NUL | find /I /N "RocketLeague.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [+] Rocket League detected!
) else (
    echo [!] Rocket League not detected
    echo     Please start Rocket League first!
    echo.
    echo Press any key to continue anyway...
    pause
)

echo.
echo [*] Running pattern finder and updater...
echo.

REM Run the simple pattern updater
python simple_pattern_updater.py

REM Check if it was successful
if errorlevel 1 (
    echo.
    echo [!] Pattern update failed or insufficient patterns found
    echo [*] Your mod will run in demo mode
    echo.
    echo Press any key to continue with demo mode...
    pause
) else (
    echo.
    echo [+] Patterns found and mod updated successfully!
    echo [*] Your mod is now fully functional!
    echo.
    echo Press any key to start the mod...
    pause
)

echo.
echo [*] Starting Rocket League Mod...
echo.

REM Start the mod
python rocket_league_mod.py

echo.
echo [*] Mod has exited
pause
