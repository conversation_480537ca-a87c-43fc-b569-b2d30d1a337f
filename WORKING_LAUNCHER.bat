@echo off
title Rocket League Mod - WORKING VERSION

echo ==========================================
echo   Rocket League Mod - WORKING VERSION
echo ==========================================
echo.
echo This launcher uses the patterns we found:
echo   car_visibility: 0x7FF708CC6B18
echo   title_string: 0x7FF708C04718
echo   player_data: 0x7FF708C5FE16
echo.
echo Features:
echo   [+] REAL car invisibility toggle
echo   [+] REAL title changing
echo   [+] Direct memory manipulation
echo   [+] No file editing needed
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [!] Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Check if Rocket League is running
echo [*] Checking for Rocket League...
tasklist /FI "IMAGENAME eq RocketLeague.exe" 2>NUL | find /I /N "RocketLeague.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [+] Rocket League detected!
) else (
    echo [!] Rocket League not detected
    echo     Please start Rocket League first!
    echo.
    echo Press any key to continue anyway...
    pause
)

echo.
echo [+] Starting WORKING Rocket League Mod...
echo.
echo Hotkeys:
echo   F1 - Toggle car invisibility (REAL)
echo   F2 - Cycle titles (REAL)
echo   F3 - Toggle GUI
echo.

REM Start the direct mod
python direct_mod.py

echo.
echo [*] Mod has exited
pause
