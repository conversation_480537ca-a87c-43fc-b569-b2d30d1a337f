#!/usr/bin/env python3
"""
Rocket League Mod - Python Edition
Main application entry point

Features:
- Car invisibility toggle
- Custom server-side titles
- Modern GUI interface
- Global hotkey support
- Configuration management
"""

import sys
import time
import signal
import threading
from typing import Optional, Dict, Any

# Import mod components
from memory_manager import RocketL<PERSON>gueMemory
from game_scanner import GameScanner
from car_invisibility import CarInvisibilitySystem
from title_system import TitleSystem
from gui_interface import ModernGUI
from config_manager import <PERSON>fig<PERSON><PERSON><PERSON>
from hotkey_manager import HotkeyManager


class RocketLeagueMod:
    """Main mod application class"""
    
    def __init__(self):
        print("🚀 Initializing Rocket League Mod...")
        
        # Core components
        self.config_manager = ConfigManager()
        self.memory_manager = RocketLeagueMemory()
        self.game_scanner = GameScanner()
        self.car_system: Optional[CarInvisibilitySystem] = None
        self.title_system: Optional[TitleSystem] = None
        self.gui: Optional[ModernGUI] = None
        self.hotkey_manager: Optional[HotkeyManager] = None
        
        # State
        self.is_running = False
        self.is_connected = False
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all mod components"""
        try:
            # Load configuration
            print("📄 Loading configuration...")
            self.config_manager.load_config()
            
            # Initialize game scanner
            print("🔍 Initializing game scanner...")
            self.game_scanner.register_callback('game_started', self._on_game_started)
            self.game_scanner.register_callback('game_stopped', self._on_game_stopped)
            
            # Initialize hotkey manager
            print("⌨️ Initializing hotkey manager...")
            self.hotkey_manager = HotkeyManager(self.config_manager)
            self._register_hotkeys()
            
            # Initialize GUI
            print("🖥️ Initializing GUI...")
            self.gui = ModernGUI()
            self._register_gui_callbacks()
            
            print("✅ All components initialized successfully!")
            
        except Exception as e:
            print(f"❌ Error initializing components: {e}")
            sys.exit(1)
    
    def _register_hotkeys(self):
        """Register global hotkey callbacks"""
        if not self.hotkey_manager:
            return
        
        self.hotkey_manager.register_callback('toggle_gui', self._toggle_gui)
        self.hotkey_manager.register_callback('toggle_car_invisibility', self._toggle_car_invisibility)
        self.hotkey_manager.register_callback('cycle_title', self._cycle_title)
        self.hotkey_manager.register_callback('emergency_restore', self._emergency_restore)
    
    def _register_gui_callbacks(self):
        """Register GUI event callbacks"""
        if not self.gui:
            return
        
        self.gui.register_callback('connect_to_game', self.connect_to_game)
        self.gui.register_callback('disconnect_from_game', self.disconnect_from_game)
        self.gui.register_callback('make_car_invisible', self._make_car_invisible)
        self.gui.register_callback('make_car_visible', self._make_car_visible)
        self.gui.register_callback('set_title_by_name', self._set_title_by_name)
        self.gui.register_callback('cycle_title', self._cycle_title)
        self.gui.register_callback('add_custom_title', self._add_custom_title)
        self.gui.register_callback('get_current_title', self._get_current_title)
        self.gui.register_callback('get_available_titles', self._get_available_titles)
        self.gui.register_callback('get_status', self._get_status)
        self.gui.register_callback('cleanup', self.cleanup)
    
    def run(self):
        """Start the mod application"""
        print("🚀 Starting Rocket League Mod...")
        self.is_running = True
        
        try:
            # Start game monitoring
            self.game_scanner.start_monitoring()
            
            # Start hotkey monitoring
            if self.hotkey_manager:
                self.hotkey_manager.start_monitoring()
            
            # Auto-connect if enabled
            config = self.config_manager.get_config()
            if config.game.auto_connect:
                print("🔄 Auto-connect enabled, searching for Rocket League...")
                threading.Thread(target=self._auto_connect_loop, daemon=True).start()
            
            # Start GUI
            if self.gui:
                print("🖥️ Starting GUI...")
                self.gui.run()
            
        except KeyboardInterrupt:
            print("\n⏹️ Interrupted by user")
        except Exception as e:
            print(f"❌ Error running mod: {e}")
        finally:
            self.cleanup()
    
    def _auto_connect_loop(self):
        """Auto-connect loop"""
        while self.is_running and not self.is_connected:
            try:
                if self.game_scanner.wait_for_game(timeout=5.0):
                    time.sleep(2)  # Wait for game to fully load
                    self.connect_to_game()
                    break
                time.sleep(1)
            except Exception as e:
                print(f"Auto-connect error: {e}")
                time.sleep(5)
    
    def connect_to_game(self) -> bool:
        """Connect to Rocket League"""
        if self.is_connected:
            print("ℹ️ Already connected to game")
            return True
        
        try:
            print("🔌 Connecting to Rocket League...")
            
            # Connect memory manager
            if not self.memory_manager.connect_to_game():
                print("❌ Failed to connect to game memory")
                return False
            
            # Initialize car system
            print("👻 Initializing car invisibility system...")
            self.car_system = CarInvisibilitySystem(self.memory_manager)
            if not self.car_system.initialize():
                print("⚠️ Car invisibility system failed to initialize")
            
            # Initialize title system
            print("🏆 Initializing title system...")
            self.title_system = TitleSystem(self.memory_manager)
            if not self.title_system.initialize():
                print("⚠️ Title system failed to initialize")
            
            # Update GUI with available titles
            if self.gui and self.title_system:
                self.gui.update_title_list()
            
            # Restore previous states if configured
            self._restore_previous_states()
            
            self.is_connected = True
            print("✅ Successfully connected to Rocket League!")
            return True
            
        except Exception as e:
            print(f"❌ Error connecting to game: {e}")
            return False
    
    def disconnect_from_game(self):
        """Disconnect from Rocket League"""
        if not self.is_connected:
            return
        
        print("🔌 Disconnecting from Rocket League...")
        
        # Cleanup systems
        if self.car_system:
            self.car_system.cleanup()
            self.car_system = None
        
        if self.title_system:
            self.title_system = None
        
        # Disconnect memory manager
        self.memory_manager.disconnect()
        
        self.is_connected = False
        print("🔌 Disconnected from Rocket League")
    
    def _restore_previous_states(self):
        """Restore previous states from config"""
        config = self.config_manager.get_config()
        
        # Restore car invisibility state
        if (config.car.remember_state and 
            config.car.last_invisible_state and 
            self.car_system):
            print("🔄 Restoring previous car invisibility state...")
            self.car_system.make_invisible()
        
        # Restore title selection
        if (config.title.remember_selection and 
            self.title_system):
            print("🔄 Restoring previous title selection...")
            self.title_system.set_title(config.title.last_selected_index)
    
    def _on_game_started(self, game_info):
        """Handle game started event"""
        print(f"🎮 Game started: {game_info.process_name}")
        if not self.is_connected:
            # Small delay to let game fully load
            threading.Timer(3.0, self.connect_to_game).start()
    
    def _on_game_stopped(self, game_info):
        """Handle game stopped event"""
        print(f"🔴 Game stopped: {game_info.process_name}")
        self.disconnect_from_game()
    
    # Hotkey callbacks
    def _toggle_gui(self):
        """Toggle GUI visibility"""
        if self.gui:
            if self.gui.root.state() == 'withdrawn':
                self.gui.show()
                print("👁️ GUI shown")
            else:
                self.gui.hide()
                print("🙈 GUI hidden")
    
    def _toggle_car_invisibility(self):
        """Toggle car invisibility"""
        if not self.car_system:
            print("❌ Car system not available")
            return
        
        success = self.car_system.toggle_invisibility()
        if success:
            state = "invisible" if self.car_system.is_car_invisible() else "visible"
            print(f"👻 Car is now {state}")
            
            # Update config
            self.config_manager.update_car_state(self.car_system.is_car_invisible())
    
    def _cycle_title(self):
        """Cycle to next title"""
        if not self.title_system:
            print("❌ Title system not available")
            return
        
        success = self.title_system.cycle_title()
        if success:
            current_title = self.title_system.get_current_title()
            if current_title:
                print(f"🏆 Title changed to: {current_title.display_text}")
                
                # Update config
                self.config_manager.update_title_selection(self.title_system.current_title_index)
    
    def _emergency_restore(self):
        """Emergency restore all settings"""
        print("🚨 Emergency restore triggered!")
        
        if self.car_system and self.car_system.is_car_invisible():
            self.car_system.make_visible()
            print("👁️ Car visibility restored")
        
        print("✅ Emergency restore completed")
    
    # GUI callbacks
    def _make_car_invisible(self) -> bool:
        """Make car invisible (GUI callback)"""
        if self.car_system:
            success = self.car_system.make_invisible()
            if success:
                self.config_manager.update_car_state(True)
            return success
        return False
    
    def _make_car_visible(self) -> bool:
        """Make car visible (GUI callback)"""
        if self.car_system:
            success = self.car_system.make_visible()
            if success:
                self.config_manager.update_car_state(False)
            return success
        return False
    
    def _set_title_by_name(self, title_name: str) -> bool:
        """Set title by name (GUI callback)"""
        if self.title_system:
            success = self.title_system.set_title_by_name(title_name)
            if success:
                self.config_manager.update_title_selection(self.title_system.current_title_index)
            return success
        return False
    
    def _add_custom_title(self, title_text: str) -> bool:
        """Add custom title (GUI callback)"""
        if self.title_system:
            success = self.title_system.add_custom_title(title_text)
            if success:
                self.config_manager.add_custom_title(title_text)
            return success
        return False
    
    def _get_current_title(self) -> Optional[str]:
        """Get current title (GUI callback)"""
        if self.title_system:
            current_title = self.title_system.get_current_title()
            return current_title.display_text if current_title else None
        return None
    
    def _get_available_titles(self):
        """Get available titles (GUI callback)"""
        if self.title_system:
            return self.title_system.get_available_titles()
        return []
    
    def _get_status(self) -> Dict[str, Any]:
        """Get current mod status (GUI callback)"""
        return {
            'connected': self.is_connected,
            'car_invisible': self.car_system.is_car_invisible() if self.car_system else False,
            'current_title': self._get_current_title() or "No Title",
            'game_running': self.game_scanner.is_game_running(),
            'memory_connected': self.memory_manager.is_connected
        }
    
    def cleanup(self):
        """Cleanup and shutdown"""
        print("🧹 Cleaning up...")
        self.is_running = False
        
        # Disconnect from game
        self.disconnect_from_game()
        
        # Stop monitoring
        if self.hotkey_manager:
            self.hotkey_manager.stop_monitoring()
        
        self.game_scanner.stop_monitoring()
        
        print("✅ Cleanup completed")


def main():
    """Main entry point"""
    print("=" * 50)
    print("🚀 Rocket League Mod - Python Edition")
    print("=" * 50)
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n⏹️ Shutting down...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Create and run mod
        mod = RocketLeagueMod()
        mod.run()
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
