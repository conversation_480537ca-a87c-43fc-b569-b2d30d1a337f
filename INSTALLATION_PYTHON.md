# 🚀 Rocket League Mod - Python Edition

## Quick Start

### Option 1: Automatic Installation (Recommended)
1. **Download** all files to a folder
2. **Double-click** `run.bat` 
3. **Follow** the on-screen instructions
4. **Start** Rocket League
5. **Enjoy** the mod!

### Option 2: Manual Installation
1. **Install Python 3.8+** from [python.org](https://python.org)
2. **Run**: `python install.py`
3. **Run**: `python rocket_league_mod.py`

## 📋 Requirements

- **Python 3.8+** (Required)
- **Windows 10/11** (Recommended)
- **Rocket League** (Obviously!)
- **Administrator privileges** (For global hotkeys)

## 🎮 Features

### ✅ Car Invisibility
- Toggle your car's visibility on/off
- Maintains collision detection
- Server-side compatible
- **Hotkey**: F1

### ✅ Custom Titles
- Use any title server-side
- Bypass normal restrictions
- Add custom titles easily
- **Hotkey**: F2 (cycle)

### ✅ Modern GUI
- Clean, dark theme interface
- Real-time status updates
- Easy configuration
- **Hotkey**: F3 (toggle)

### ✅ Global Hotkeys
- Works even when game is focused
- Customizable key bindings
- Emergency restore function
- **Emergency**: F4

## ⌨️ Default Hotkeys

| Key | Function |
|-----|----------|
| **F1** | Toggle car invisibility |
| **F2** | Cycle through titles |
| **F3** | Show/hide GUI |
| **F4** | Emergency restore all |

## 📁 File Structure

```
rocket_league_mod/
├── rocket_league_mod.py      # Main application
├── memory_manager.py         # Memory manipulation
├── game_scanner.py          # Process detection
├── car_invisibility.py      # Car visibility system
├── title_system.py          # Title management
├── gui_interface.py         # User interface
├── config_manager.py        # Settings management
├── hotkey_manager.py        # Global hotkeys
├── install.py               # Installation script
├── run.bat                  # Quick launcher
├── requirements.txt         # Dependencies
└── README_PYTHON.md         # This file
```

## 🔧 Configuration

The mod creates a `rl_mod_config.json` file with all settings:

```json
{
  "hotkeys": {
    "toggle_gui": "f3",
    "toggle_car_invisibility": "f1",
    "cycle_title": "f2",
    "emergency_restore": "f4"
  },
  "ui": {
    "theme": "dark",
    "window_width": 800,
    "window_height": 600
  },
  "game": {
    "auto_connect": true,
    "connection_timeout": 30
  }
}
```

## 🛠️ Troubleshooting

### "Python not found"
- Install Python from [python.org](https://python.org)
- Make sure to check "Add to PATH" during installation

### "Dependencies not found"
- Run: `python install.py`
- Or manually: `pip install -r requirements.txt`

### "Failed to connect to game"
- Make sure Rocket League is running
- Try running as Administrator
- Check Windows Firewall settings

### "Hotkeys not working"
- Run as Administrator
- Check for conflicting software
- Try different key combinations

### "Car invisibility not working"
- Memory patterns may need updating for your game version
- Check the console for error messages
- Try the "Force Refresh" option in GUI

## 🔍 Advanced Usage

### Custom Memory Patterns
If the mod doesn't work with your game version, you may need to update memory patterns in:
- `memory_manager.py` - Game detection patterns
- `car_invisibility.py` - Car visibility patterns  
- `title_system.py` - Title system patterns

### Adding Custom Titles
1. Use the GUI to add titles
2. Or edit `custom_titles.json` directly
3. Restart the mod to reload

### Hotkey Customization
1. Edit `rl_mod_config.json`
2. Or use the GUI settings (coming soon)
3. Restart to apply changes

## ⚠️ Important Notes

- **Educational purposes only**
- **Use at your own risk**
- **May trigger anti-cheat systems**
- **Memory patterns are game-version specific**
- **Backup your game saves**

## 🆚 Python vs C++ Version

| Feature | Python | C++ DLL |
|---------|--------|---------|
| **Ease of use** | ✅ Very easy | ❌ Complex |
| **No compilation** | ✅ Ready to run | ❌ Must compile |
| **Debugging** | ✅ Excellent | ❌ Difficult |
| **Updates** | ✅ Edit and run | ❌ Recompile needed |
| **Safety** | ✅ External tool | ⚠️ Injection required |
| **Detection risk** | ✅ Lower | ⚠️ Higher |

## 🤝 Contributing

Want to improve the mod?
1. Fork the repository
2. Make your changes
3. Test thoroughly
4. Submit a pull request

## 📞 Support

Having issues?
1. Check this README
2. Look at console error messages
3. Try running as Administrator
4. Update your game patterns

## 🎯 Next Steps

After installation:
1. **Start Rocket League**
2. **Run the mod** (`run.bat` or `python rocket_league_mod.py`)
3. **Press F3** to open the GUI
4. **Connect** to the game
5. **Enjoy** your new powers!

---

**Happy modding! 🚀**
