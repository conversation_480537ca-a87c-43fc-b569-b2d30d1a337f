#!/usr/bin/env python3
"""
Smart Launcher - Automatically updates patterns and launches mod
"""

import subprocess
import sys
import os
import time


def run_auto_updater():
    """Run the auto pattern updater"""
    print("[*] Running auto pattern updater...")

    try:
        # Run the simple auto updater (no Unicode issues)
        result = subprocess.run([sys.executable, "simple_pattern_updater.py"],
                              capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print("[+] Pattern updater completed successfully!")
            return True
        else:
            print("[!] Pattern updater failed:")
            print(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        print("[!] Pattern updater timed out")
        return False
    except Exception as e:
        print(f"[!] Error running pattern updater: {e}")
        return False


def run_main_mod():
    """Run the main mod"""
    print("[*] Starting Rocket League Mod...")

    try:
        # Run the main mod
        subprocess.run([sys.executable, "rocket_league_mod.py"])

    except KeyboardInterrupt:
        print("\n[!] Mod stopped by user")
    except Exception as e:
        print(f"[!] Error running mod: {e}")


def check_dependencies():
    """Check if all required files exist"""
    required_files = [
        "simple_pattern_updater.py",
        "rocket_league_mod.py",
        "memory_manager.py",
        "car_invisibility.py",
        "title_system.py"
    ]

    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("[!] Missing required files:")
        for file in missing_files:
            print(f"    - {file}")
        return False

    return True


def main():
    """Main launcher function"""
    print("Smart Rocket League Mod Launcher")
    print("=" * 40)

    # Check dependencies
    if not check_dependencies():
        print("\n[!] Cannot start - missing files")
        input("Press Enter to exit...")
        return

    print("[+] All files present")
    
    # Option 1: Auto-update and run
    print("\n[*] Choose launch mode:")
    print("1. Smart Launch (Auto-update patterns + Run mod)")
    print("2. Run mod only (Skip pattern update)")
    print("3. Update patterns only (Don't run mod)")
    print("4. Exit")

    choice = input("\nEnter choice (1-4): ").strip()

    if choice == "1":
        print("\n[*] Smart Launch Mode")
        print("=" * 20)

        # Run auto updater
        if run_auto_updater():
            print("\n[+] Patterns updated! Starting mod...")
            time.sleep(2)
            run_main_mod()
        else:
            print("\n[!] Pattern update failed. Running mod in demo mode...")
            time.sleep(2)
            run_main_mod()

    elif choice == "2":
        print("\n[*] Running mod without pattern update...")
        run_main_mod()

    elif choice == "3":
        print("\n[*] Updating patterns only...")
        run_auto_updater()

    elif choice == "4":
        print("[*] Goodbye!")
        return

    else:
        print("[!] Invalid choice")
        return


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[!] Cancelled by user")
    except Exception as e:
        print(f"\n[!] Unexpected error: {e}")
        input("Press Enter to exit...")
