#pragma once
#include "pch.h"

struct ModConfig {
    // Car invisibility settings
    bool carInvisibilityEnabled;
    bool rememberCarInvisibilityState;
    
    // Title settings
    int selectedTitleIndex;
    std::string customTitle;
    std::vector<std::string> customTitles;
    bool rememberTitleSelection;
    
    // UI settings
    bool showUIOnStartup;
    float uiScale;
    ImVec2 mainWindowPos;
    ImVec2 mainWindowSize;
    
    // Hotkey settings
    int toggleUIKey;
    int toggleCarInvisibilityKey;
    int cycleTitleKey;
    
    // Advanced settings
    bool enableLogging;
    std::string logLevel;
    bool autoInjectOnGameStart;
    
    ModConfig() {
        // Default values
        carInvisibilityEnabled = false;
        rememberCarInvisibilityState = true;
        selectedTitleIndex = 0;
        customTitle = "";
        rememberTitleSelection = true;
        showUIOnStartup = false;
        uiScale = 1.0f;
        mainWindowPos = ImVec2(100, 100);
        mainWindowSize = ImVec2(400, 300);
        toggleUIKey = VK_INSERT;
        toggleCarInvisibilityKey = VK_F1;
        cycleTitleKey = VK_F2;
        enableLogging = false;
        logLevel = "INFO";
        autoInjectOnGameStart = false;
    }
};

class Config
{
private:
    ModConfig m_config;
    std::string m_configPath;
    bool m_bLoaded;
    
public:
    Config();
    ~Config();
    
    bool Load();
    bool Save();
    
    // Getters
    const ModConfig& GetConfig() const { return m_config; }
    ModConfig& GetConfig() { return m_config; }
    
    // Specific getters
    bool IsCarInvisibilityEnabled() const { return m_config.carInvisibilityEnabled; }
    int GetSelectedTitleIndex() const { return m_config.selectedTitleIndex; }
    const std::string& GetCustomTitle() const { return m_config.customTitle; }
    const std::vector<std::string>& GetCustomTitles() const { return m_config.customTitles; }
    
    // Specific setters
    void SetCarInvisibilityEnabled(bool enabled);
    void SetSelectedTitleIndex(int index);
    void SetCustomTitle(const std::string& title);
    void AddCustomTitle(const std::string& title);
    void RemoveCustomTitle(const std::string& title);
    
    // UI settings
    void SetUIPosition(const ImVec2& pos) { m_config.mainWindowPos = pos; }
    void SetUISize(const ImVec2& size) { m_config.mainWindowSize = size; }
    ImVec2 GetUIPosition() const { return m_config.mainWindowPos; }
    ImVec2 GetUISize() const { return m_config.mainWindowSize; }
    
private:
    json ConfigToJson() const;
    void JsonToConfig(const json& j);
    std::string GetConfigPath() const;
};
