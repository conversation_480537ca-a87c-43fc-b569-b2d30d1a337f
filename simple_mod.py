#!/usr/bin/env python3
"""
Simple Rocket League Mod - Basic Version
Works with basic memory manipulation without complex patterns
"""

import pymem
import psutil
import keyboard
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import json


class SimpleRLMod:
    """Simple version of the Rocket League mod"""
    
    def __init__(self):
        self.process = None
        self.connected = False
        self.running = False
        
        # Simple state tracking
        self.car_invisible = False
        self.current_title_index = 0
        
        # Basic titles list
        self.titles = [
            "Rookie", "Semi-Pro", "Pro", "Veteran", "Expert",
            "Master", "Legend", "Rocketeer", "Grand Champion",
            "Supersonic Legend", "RLCS Champion", "Psyonix",
            "Custom Champion", "Mod User", "Python Master"
        ]
        
        # Create GUI
        self.create_gui()
        
        # Start hotkey monitoring
        self.start_hotkeys()
    
    def create_gui(self):
        """Create simple GUI"""
        self.root = tk.Tk()
        self.root.title("🚀 Simple Rocket League Mod")
        self.root.geometry("500x400")
        self.root.configure(bg='#2b2b2b')
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TLabel', background='#2b2b2b', foreground='white')
        style.configure('TButton', background='#404040', foreground='white')
        style.configure('TFrame', background='#2b2b2b')
        
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 Simple Rocket League Mod", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # Connection status
        self.status_label = ttk.Label(main_frame, text="❌ Not Connected", 
                                     font=('Arial', 12))
        self.status_label.pack(pady=5)
        
        # Connect button
        self.connect_btn = ttk.Button(main_frame, text="Connect to Rocket League",
                                     command=self.connect_to_game)
        self.connect_btn.pack(pady=10)
        
        # Car invisibility section
        car_frame = ttk.LabelFrame(main_frame, text="👻 Car Invisibility", padding=10)
        car_frame.pack(fill='x', pady=10)
        
        self.car_status = ttk.Label(car_frame, text="Status: Visible")
        self.car_status.pack(pady=5)
        
        car_btn = ttk.Button(car_frame, text="Toggle Invisibility (F1)",
                            command=self.toggle_car_invisibility)
        car_btn.pack(pady=5)
        
        # Title section
        title_frame = ttk.LabelFrame(main_frame, text="🏆 Title System", padding=10)
        title_frame.pack(fill='x', pady=10)
        
        self.title_status = ttk.Label(title_frame, text=f"Current: {self.titles[0]}")
        self.title_status.pack(pady=5)
        
        title_btn = ttk.Button(title_frame, text="Cycle Title (F2)",
                              command=self.cycle_title)
        title_btn.pack(pady=5)
        
        # Hotkeys info
        hotkey_frame = ttk.LabelFrame(main_frame, text="⌨️ Hotkeys", padding=10)
        hotkey_frame.pack(fill='x', pady=10)
        
        hotkey_text = ttk.Label(hotkey_frame, 
                               text="F1: Toggle Car Invisibility\nF2: Cycle Titles\nF3: Toggle GUI")
        hotkey_text.pack()
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="📝 Activity Log", padding=10)
        log_frame.pack(fill='both', expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=8, bg='#1e1e1e', fg='white',
                               font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)
        
        # Initial log message
        self.log("🚀 Simple Rocket League Mod started")
        self.log("💡 This version demonstrates the framework without complex memory patterns")
        self.log("🔧 Use the pattern finder tool to get working patterns for your game version")
    
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def connect_to_game(self):
        """Connect to Rocket League"""
        try:
            # Find Rocket League process
            for proc in psutil.process_iter(['name']):
                if 'rocket' in proc.info['name'].lower():
                    process_name = proc.info['name']
                    break
            else:
                self.log("❌ Rocket League not found - please start the game first")
                messagebox.showerror("Error", "Rocket League not found!\nPlease start the game first.")
                return
            
            # Connect to process
            self.process = pymem.Pymem(process_name)
            self.connected = True
            
            self.log(f"✅ Connected to {process_name}")
            self.log(f"📍 Process ID: {self.process.process_id}")
            self.log(f"📍 Base Address: 0x{self.process.base_address:X}")
            
            self.status_label.config(text="✅ Connected")
            self.connect_btn.config(text="Disconnect", command=self.disconnect)
            
            messagebox.showinfo("Success", "Connected to Rocket League!\n\nNote: This is a demo version.\nUse the pattern finder to get working patterns.")
            
        except Exception as e:
            self.log(f"❌ Connection failed: {e}")
            messagebox.showerror("Error", f"Failed to connect:\n{e}")
    
    def disconnect(self):
        """Disconnect from game"""
        if self.process:
            self.process.close_process()
        
        self.connected = False
        self.log("🔌 Disconnected from Rocket League")
        self.status_label.config(text="❌ Not Connected")
        self.connect_btn.config(text="Connect to Rocket League", command=self.connect_to_game)
    
    def toggle_car_invisibility(self):
        """Toggle car invisibility (demo)"""
        if not self.connected:
            self.log("❌ Not connected to game")
            messagebox.showwarning("Warning", "Please connect to Rocket League first")
            return
        
        self.car_invisible = not self.car_invisible
        status = "Invisible" if self.car_invisible else "Visible"
        
        self.log(f"👻 Car invisibility: {status}")
        self.car_status.config(text=f"Status: {status}")
        
        if self.car_invisible:
            self.log("💡 Demo mode: Car would be invisible with correct memory patterns")
        else:
            self.log("💡 Demo mode: Car would be visible with correct memory patterns")
    
    def cycle_title(self):
        """Cycle through titles (demo)"""
        if not self.connected:
            self.log("❌ Not connected to game")
            messagebox.showwarning("Warning", "Please connect to Rocket League first")
            return
        
        self.current_title_index = (self.current_title_index + 1) % len(self.titles)
        current_title = self.titles[self.current_title_index]
        
        self.log(f"🏆 Title changed to: {current_title}")
        self.title_status.config(text=f"Current: {current_title}")
        self.log("💡 Demo mode: Title would change with correct memory patterns")
    
    def start_hotkeys(self):
        """Start hotkey monitoring"""
        def hotkey_thread():
            try:
                keyboard.add_hotkey('f1', self.toggle_car_invisibility)
                keyboard.add_hotkey('f2', self.cycle_title)
                keyboard.add_hotkey('f3', self.toggle_gui)
                
                self.log("⌨️ Hotkeys registered: F1, F2, F3")
                
                # Keep thread alive
                while self.running:
                    time.sleep(0.1)
                    
            except Exception as e:
                self.log(f"❌ Hotkey error: {e}")
        
        self.running = True
        threading.Thread(target=hotkey_thread, daemon=True).start()
    
    def toggle_gui(self):
        """Toggle GUI visibility"""
        if self.root.state() == 'withdrawn':
            self.root.deiconify()
            self.log("👁️ GUI shown")
        else:
            self.root.withdraw()
            self.log("🙈 GUI hidden")
    
    def run(self):
        """Run the mod"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        if self.connected:
            self.disconnect()
        self.root.quit()
        self.root.destroy()


def main():
    """Main function"""
    print("🚀 Simple Rocket League Mod")
    print("=" * 30)
    print("This is a simplified version that demonstrates the framework")
    print("without complex memory patterns that need game-specific updates.")
    print()
    print("To get full functionality:")
    print("1. Run pattern_finder.py to find correct memory patterns")
    print("2. Update the main mod with the found patterns")
    print("3. Or use this simple version to test the basic framework")
    print()
    
    try:
        mod = SimpleRLMod()
        mod.run()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
