"""
Hotkey Manager - Global hotkey detection and handling
"""

import keyboard
import threading
import time
from typing import Dict, Callable, Optional, Any
from config_manager import ConfigManager


class HotkeyManager:
    """Manages global hotkeys for the mod"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.callbacks: Dict[str, Callable] = {}
        self.is_running = False
        self.hotkey_thread: Optional[threading.Thread] = None
        
        # Hotkey states to prevent spam
        self.key_states: Dict[str, bool] = {}
        self.last_press_times: Dict[str, float] = {}
        self.debounce_time = 0.2  # 200ms debounce
        
        # Current hotkey bindings
        self.hotkeys = {}
        self._load_hotkeys()
    
    def _load_hotkeys(self):
        """Load hotkey bindings from config"""
        config = self.config_manager.get_config()
        self.hotkeys = {
            'toggle_gui': config.hotkeys.toggle_gui,
            'toggle_car_invisibility': config.hotkeys.toggle_car_invisibility,
            'cycle_title': config.hotkeys.cycle_title,
            'emergency_restore': config.hotkeys.emergency_restore
        }
        
        # Initialize key states
        for action, key in self.hotkeys.items():
            self.key_states[key] = False
            self.last_press_times[key] = 0
    
    def start_monitoring(self):
        """Start monitoring for hotkeys"""
        if self.is_running:
            return
        
        self.is_running = True
        self.hotkey_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.hotkey_thread.start()
        print("⌨️ Hotkey monitoring started")
        
        # Register keyboard hooks
        self._register_hooks()
    
    def stop_monitoring(self):
        """Stop monitoring hotkeys"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Unregister keyboard hooks
        self._unregister_hooks()
        
        if self.hotkey_thread:
            self.hotkey_thread.join(timeout=1.0)
        
        print("⌨️ Hotkey monitoring stopped")
    
    def _register_hooks(self):
        """Register keyboard hooks for all hotkeys"""
        try:
            for action, key in self.hotkeys.items():
                keyboard.on_press_key(key, lambda e, a=action: self._on_key_press(a, e))
            print(f"✅ Registered hotkeys: {list(self.hotkeys.values())}")
        except Exception as e:
            print(f"❌ Error registering hotkeys: {e}")
    
    def _unregister_hooks(self):
        """Unregister all keyboard hooks"""
        try:
            keyboard.unhook_all()
            print("🔓 Unregistered all hotkeys")
        except Exception as e:
            print(f"❌ Error unregistering hotkeys: {e}")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                time.sleep(0.1)  # Small delay to prevent high CPU usage
            except Exception as e:
                print(f"❌ Hotkey monitor error: {e}")
                time.sleep(1.0)
    
    def _on_key_press(self, action: str, event):
        """Handle key press events"""
        try:
            key = self.hotkeys.get(action)
            if not key:
                return
            
            current_time = time.time()
            
            # Check debounce
            if current_time - self.last_press_times.get(key, 0) < self.debounce_time:
                return
            
            self.last_press_times[key] = current_time
            
            # Trigger callback
            self._trigger_callback(action)
            
        except Exception as e:
            print(f"❌ Error handling key press: {e}")
    
    def register_callback(self, action: str, callback: Callable):
        """Register callback for hotkey action"""
        self.callbacks[action] = callback
        print(f"✅ Registered callback for {action}")
    
    def _trigger_callback(self, action: str):
        """Trigger registered callback"""
        if action in self.callbacks:
            try:
                print(f"⌨️ Hotkey triggered: {action} ({self.hotkeys.get(action, 'unknown')})")
                self.callbacks[action]()
            except Exception as e:
                print(f"❌ Callback error for {action}: {e}")
    
    def update_hotkey(self, action: str, new_key: str) -> bool:
        """Update a hotkey binding"""
        try:
            # Validate the new key
            if not self._validate_key(new_key):
                print(f"❌ Invalid key: {new_key}")
                return False
            
            # Check for conflicts
            if new_key in self.hotkeys.values():
                print(f"❌ Key {new_key} is already bound to another action")
                return False
            
            # Update hotkey
            old_key = self.hotkeys.get(action)
            self.hotkeys[action] = new_key
            
            # Update config
            if self.config_manager.update_hotkey(action, new_key):
                # Re-register hooks
                self._unregister_hooks()
                self._register_hooks()
                
                print(f"✅ Updated {action}: {old_key} → {new_key}")
                return True
            else:
                # Revert on config save failure
                self.hotkeys[action] = old_key
                return False
                
        except Exception as e:
            print(f"❌ Error updating hotkey: {e}")
            return False
    
    def _validate_key(self, key: str) -> bool:
        """Validate if a key is valid"""
        try:
            # Try to parse the key
            keyboard.parse_hotkey(key)
            return True
        except Exception:
            return False
    
    def get_hotkeys(self) -> Dict[str, str]:
        """Get current hotkey bindings"""
        return self.hotkeys.copy()
    
    def get_hotkey_info(self) -> Dict[str, Any]:
        """Get detailed hotkey information"""
        return {
            'bindings': self.hotkeys.copy(),
            'is_monitoring': self.is_running,
            'debounce_time': self.debounce_time,
            'registered_callbacks': list(self.callbacks.keys())
        }
    
    def test_hotkey(self, key: str) -> bool:
        """Test if a hotkey is working"""
        try:
            def test_callback():
                print(f"🧪 Test hotkey triggered: {key}")
            
            # Temporarily register test callback
            test_hook = keyboard.on_press_key(key, lambda e: test_callback())
            
            print(f"🧪 Testing hotkey: {key} (press the key to test)")
            time.sleep(3)  # Wait 3 seconds for test
            
            # Remove test hook
            keyboard.unhook(test_hook)
            return True
            
        except Exception as e:
            print(f"❌ Error testing hotkey {key}: {e}")
            return False
    
    def reload_config(self):
        """Reload hotkey configuration"""
        print("🔄 Reloading hotkey configuration...")
        
        # Stop current monitoring
        was_running = self.is_running
        if was_running:
            self.stop_monitoring()
        
        # Reload hotkeys from config
        self._load_hotkeys()
        
        # Restart monitoring if it was running
        if was_running:
            self.start_monitoring()
        
        print("✅ Hotkey configuration reloaded")
    
    def get_available_keys(self) -> list:
        """Get list of available keys for binding"""
        return [
            'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
            'ctrl+f1', 'ctrl+f2', 'ctrl+f3', 'ctrl+f4',
            'alt+f1', 'alt+f2', 'alt+f3', 'alt+f4',
            'shift+f1', 'shift+f2', 'shift+f3', 'shift+f4',
            'insert', 'delete', 'home', 'end', 'page up', 'page down',
            'ctrl+shift+f1', 'ctrl+shift+f2', 'ctrl+shift+f3', 'ctrl+shift+f4',
            'numpad 0', 'numpad 1', 'numpad 2', 'numpad 3', 'numpad 4',
            'numpad 5', 'numpad 6', 'numpad 7', 'numpad 8', 'numpad 9'
        ]
    
    def check_conflicts(self) -> Dict[str, list]:
        """Check for hotkey conflicts"""
        conflicts = {}
        key_to_actions = {}
        
        # Build reverse mapping
        for action, key in self.hotkeys.items():
            if key not in key_to_actions:
                key_to_actions[key] = []
            key_to_actions[key].append(action)
        
        # Find conflicts
        for key, actions in key_to_actions.items():
            if len(actions) > 1:
                conflicts[key] = actions
        
        return conflicts
    
    def emergency_disable_all(self):
        """Emergency disable all hotkeys"""
        print("🚨 Emergency: Disabling all hotkeys")
        self.stop_monitoring()
        self.callbacks.clear()
    
    def is_key_pressed(self, key: str) -> bool:
        """Check if a specific key is currently pressed"""
        try:
            return keyboard.is_pressed(key)
        except Exception:
            return False
