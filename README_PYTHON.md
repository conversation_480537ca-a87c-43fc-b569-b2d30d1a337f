# Rocket League Mod - Python Edition

A Python-based external tool for Rocket League that provides:
- 🚗 **Toggleable car invisibility**
- 🏆 **Custom server-side titles**
- 🎮 **User-friendly GUI interface**
- ⌨️ **Global hotkey support**

## Why Python?

✅ **Easier to modify and customize**  
✅ **No compilation required**  
✅ **Better debugging and error handling**  
✅ **Cross-platform potential**  
✅ **Simpler dependency management**  
✅ **More readable code**

## Features

### Car Invisibility
- Toggle your car's visibility on/off
- Works by modifying memory values
- Maintains collision detection
- Instant toggle with hotkeys

### Custom Titles
- Use any title server-side
- Bypass normal restrictions
- Includes popular titles + custom ones
- Appears to other players online

### GUI Interface
- Modern, clean interface
- Real-time status updates
- Easy configuration
- Hotkey customization

## Quick Start

1. **Install Python 3.8+**
2. **Install dependencies**: `pip install -r requirements.txt`
3. **Run the mod**: `python rocket_league_mod.py`
4. **Start Rocket League**
5. **Use hotkeys or GUI to control features**

## Default Hotkeys

- **F1**: Toggle car invisibility
- **F2**: Cycle through titles
- **F3**: Toggle GUI visibility
- **ESC**: Exit mod (when GUI focused)

## File Structure

```
rocket_league_mod/
├── rocket_league_mod.py      # Main application
├── memory_manager.py         # Memory manipulation
├── car_invisibility.py       # Car visibility system
├── title_system.py          # Title management
├── gui_interface.py         # User interface
├── config_manager.py        # Settings management
├── hotkey_manager.py        # Global hotkeys
├── game_scanner.py          # Process detection
├── config.json              # User settings
└── requirements.txt         # Dependencies
```

## Advantages over C++ DLL

- **No injection required** - Works externally
- **Safer** - Less likely to trigger anti-cheat
- **Easier updates** - No recompilation needed
- **Better error handling** - Python's exception system
- **Rapid development** - Quick testing and iteration
- **User-friendly** - Easier for others to modify

## Technical Approach

Instead of DLL injection, this Python version:
1. **Finds Rocket League process** using psutil
2. **Reads/writes memory** using pymem
3. **Scans for patterns** to locate game functions
4. **Modifies values** directly in memory
5. **Provides GUI** for easy control

This approach is often more stable and less detectable than DLL injection.
