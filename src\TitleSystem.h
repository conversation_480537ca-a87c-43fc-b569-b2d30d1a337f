#pragma once
#include "pch.h"

struct TitleInfo {
    std::string name;
    std::string displayText;
    int titleId;
    bool isCustom;
    
    TitleInfo(const std::string& n, const std::string& d, int id, bool custom = false)
        : name(n), displayText(d), titleId(id), isCustom(custom) {}
};

class TitleSystem
{
private:
    bool m_bInitialized;
    int m_currentTitleIndex;
    std::vector<TitleInfo> m_availableTitles;
    std::string m_customTitle;
    
    // Hook addresses
    void* m_pGetPlayerTitleFunction;
    void* m_pSetPlayerTitleFunction;
    void* m_pSendTitleDataFunction;
    void* m_pReceiveTitleDataFunction;
    
    // Original function pointers
    typedef const char*(__fastcall* GetPlayerTitleFunc)(void* playerObject);
    typedef void(__fastcall* SetPlayerTitleFunc)(void* playerObject, const char* title);
    typedef void(__fastcall* SendTitleDataFunc)(void* networkManager, void* titleData);
    typedef void(__fastcall* ReceiveTitleDataFunc)(void* networkManager, void* titleData);
    
    GetPlayerTitleFunc m_originalGetPlayerTitle;
    SetPlayerTitleFunc m_originalSetPlayerTitle;
    SendTitleDataFunc m_originalSendTitleData;
    ReceiveTitleDataFunc m_originalReceiveTitleData;
    
    // Player data
    void* m_pLocalPlayer;
    std::map<void*, std::string> m_playerTitleOverrides;
    
public:
    TitleSystem();
    ~TitleSystem();
    
    bool Initialize();
    void Update();
    void Shutdown();
    
    // Title management
    void CycleTitle();
    void SetTitle(int index);
    void SetCustomTitle(const std::string& title);
    std::string GetCurrentTitle() const;
    const std::vector<TitleInfo>& GetAvailableTitles() const { return m_availableTitles; }
    int GetCurrentTitleIndex() const { return m_currentTitleIndex; }
    
    // Hook functions
    static const char* __fastcall HookedGetPlayerTitle(void* playerObject);
    static void __fastcall HookedSetPlayerTitle(void* playerObject, const char* title);
    static void __fastcall HookedSendTitleData(void* networkManager, void* titleData);
    static void __fastcall HookedReceiveTitleData(void* networkManager, void* titleData);
    
private:
    bool FindTitleFunctions();
    void InitializeDefaultTitles();
    void LoadCustomTitles();
    bool IsLocalPlayer(void* playerObject);
    void OverridePlayerTitle(void* playerObject, const std::string& title);
    
    // Network manipulation
    void ModifyOutgoingTitleData(void* titleData);
    void ModifyIncomingTitleData(void* titleData);
};
