@echo off
echo Setting up dependencies for Rocket League Mod...

REM Create external directory structure
if not exist imgui mkdir imgui
if not exist json mkdir json

echo.
echo Please download the following dependencies:
echo.
echo 1. ImGui (https://github.com/ocornut/imgui)
echo    - Download and extract to external/imgui/
echo    - Required files: imgui.h, imgui.cpp, imgui_demo.cpp, imgui_draw.cpp,
echo      imgui_tables.cpp, imgui_widgets.cpp, imgui_impl_win32.h,
echo      imgui_impl_win32.cpp, imgui_impl_dx11.h, imgui_impl_dx11.cpp
echo.
echo 2. nlohmann/json (https://github.com/nlohmann/json)
echo    - Download single header file json.hpp
echo    - Place in external/json/include/nlohmann/json.hpp
echo.
echo Alternative: Use vcpkg to install dependencies:
echo    vcpkg install imgui[dx11-binding,win32-binding] nlohmann-json
echo.

pause
