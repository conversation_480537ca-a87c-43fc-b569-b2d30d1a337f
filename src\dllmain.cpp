#include "pch.h"
#include "RLMod.h"
#include "UI.h"
#include "CarInvisibility.h"
#include "TitleSystem.h"
#include <thread>

// Global instances
RLMod* g_pMod = nullptr;
UI* g_pUI = nullptr;
CarInvisibility* g_pCarInvis = nullptr;
TitleSystem* g_pTitleSystem = nullptr;

// Thread for mod initialization
DWORD WINAPI ModThread(LPVOID lpParam)
{
    // Wait for game to fully load
    Sleep(5000);

    try {
        // Initialize mod components
        g_pMod = new RLMod();
        g_pUI = new UI();
        g_pCarInvis = new CarInvisibility();
        g_pTitleSystem = new TitleSystem();

        // Initialize all systems
        if (!g_pMod->Initialize()) {
            MessageBoxA(NULL, "Failed to initialize RLMod", "Error", MB_OK);
            return 1;
        }

        if (!g_pCarInvis->Initialize()) {
            MessageBoxA(NULL, "Failed to initialize Car Invisibility", "Error", MB_OK);
            return 1;
        }

        if (!g_pTitleSystem->Initialize()) {
            MessageBoxA(NULL, "Failed to initialize Title System", "Error", MB_OK);
            return 1;
        }

        if (!g_pUI->Initialize()) {
            MessageBoxA(NULL, "Failed to initialize UI", "Error", MB_OK);
            return 1;
        }

        // Main mod loop
        while (true) {
            g_pMod->Update();
            g_pUI->Update();
            Sleep(16); // ~60 FPS
        }
    }
    catch (const std::exception& e) {
        MessageBoxA(NULL, e.what(), "RLMod Error", MB_OK);
    }

    return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, ModThread, hModule, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        // Cleanup
        if (g_pUI) {
            g_pUI->Shutdown();
            delete g_pUI;
        }
        if (g_pCarInvis) {
            g_pCarInvis->Shutdown();
            delete g_pCarInvis;
        }
        if (g_pTitleSystem) {
            g_pTitleSystem->Shutdown();
            delete g_pTitleSystem;
        }
        if (g_pMod) {
            g_pMod->Shutdown();
            delete g_pMod;
        }
        break;
    }
    return TRUE;
}
