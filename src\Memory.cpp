#include "pch.h"

namespace Memory {
    
    bool WriteMemory(void* address, const void* data, size_t size)
    {
        DWORD oldProtect;
        if (!VirtualProtect(address, size, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            return false;
        }
        
        memcpy(address, data, size);
        
        VirtualProtect(address, size, oldProtect, &oldProtect);
        return true;
    }
    
    bool ReadMemory(void* address, void* buffer, size_t size)
    {
        try {
            memcpy(buffer, address, size);
            return true;
        }
        catch (...) {
            return false;
        }
    }
    
    void* FindPattern(const char* pattern, const char* mask)
    {
        MODULEINFO moduleInfo;
        if (!GetModuleInformation(GetCurrentProcess(), GetModuleHandleA(nullptr), &moduleInfo, sizeof(moduleInfo))) {
            return nullptr;
        }
        
        uintptr_t moduleBase = reinterpret_cast<uintptr_t>(moduleInfo.lpBaseOfDll);
        size_t moduleSize = moduleInfo.SizeOfImage;
        
        size_t patternLength = strlen(mask);
        
        for (size_t i = 0; i < moduleSize - patternLength; i++) {
            bool found = true;
            for (size_t j = 0; j < patternLength; j++) {
                if (mask[j] != '?' && pattern[j] != *reinterpret_cast<char*>(moduleBase + i + j)) {
                    found = false;
                    break;
                }
            }
            
            if (found) {
                return reinterpret_cast<void*>(moduleBase + i);
            }
        }
        
        return nullptr;
    }
    
    DWORD GetProcessId(const wchar_t* processName)
    {
        PROCESSENTRY32W processEntry;
        processEntry.dwSize = sizeof(PROCESSENTRY32W);
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }
        
        if (Process32FirstW(snapshot, &processEntry)) {
            do {
                if (wcscmp(processEntry.szExeFile, processName) == 0) {
                    CloseHandle(snapshot);
                    return processEntry.th32ProcessID;
                }
            } while (Process32NextW(snapshot, &processEntry));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
    
    uintptr_t GetModuleBaseAddress(DWORD processId, const wchar_t* moduleName)
    {
        MODULEENTRY32W moduleEntry;
        moduleEntry.dwSize = sizeof(MODULEENTRY32W);
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, processId);
        if (snapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }
        
        if (Module32FirstW(snapshot, &moduleEntry)) {
            do {
                if (wcscmp(moduleEntry.szModule, moduleName) == 0) {
                    CloseHandle(snapshot);
                    return reinterpret_cast<uintptr_t>(moduleEntry.modBaseAddr);
                }
            } while (Module32NextW(snapshot, &moduleEntry));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
}
