@echo off
title Smart Rocket League Mod Launcher

echo ========================================
echo   🚀 Smart Rocket League Mod Launcher
echo ========================================
echo.
echo This launcher will:
echo   1. Check for Rocket League
echo   2. Find memory patterns automatically
echo   3. Update your mod files
echo   4. Launch the fully functional mod
echo.
echo ⚠️ Make sure Rocket League is running first!
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Check if Rocket League is running
echo 🔍 Checking for Rocket League...
tasklist /FI "IMAGENAME eq RocketLeague.exe" 2>NUL | find /I /N "RocketLeague.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Rocket League detected!
) else (
    echo ⚠️ Rocket League not detected
    echo Please start Rocket League first, then press any key to continue
    pause
)

echo.
echo 🚀 Starting Smart Launcher...
echo.

python smart_launcher.py

echo.
echo 👋 Launcher finished
pause
