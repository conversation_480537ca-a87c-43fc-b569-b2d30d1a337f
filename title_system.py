"""
Title System - Manages custom server-side titles
"""

import json
import time
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from memory_manager import RocketLeagueMemory


@dataclass
class Title:
    """Represents a player title"""
    id: int
    name: str
    display_text: str
    is_custom: bool = False
    rarity: str = "Common"
    unlock_method: str = "Default"


class TitleSystem:
    """Manages player titles and server-side spoofing"""
    
    def __init__(self, memory_manager: RocketLeagueMemory):
        self.memory = memory_manager
        self.current_title_index = 0
        self.available_titles: List[Title] = []
        self.custom_titles: List[str] = []
        
        # Memory addresses for title system
        self.title_addresses = {
            'player_title_ptr': None,
            'title_display_func': None,
            'network_title_data': None,
            'local_player_title': None,
        }
        
        # Title memory patterns
        self.title_patterns = {
            'title_string': {
                'pattern': b'\x48\x8D\x15\x00\x00\x00\x00\x48\x8B\xCF\xE8\x00\x00\x00\x00\x48\x8B\x4C\x24',
                'mask': 'xxx????xxxx????xxxx',
                'offset': 3
            },
            'player_title_offset': {
                'pattern': b'\x48\x8B\x80\x00\x00\x00\x00\x48\x85\xC0\x74\x00\x48\x8B\x00',
                'mask': 'xxx????xxxx?xx?',
                'offset': 3
            }
        }
        
        # Initialize default titles
        self._load_default_titles()
        self._load_custom_titles()
    
    def initialize(self) -> bool:
        """Initialize the title system"""
        if not self.memory.is_connected:
            print("❌ Memory manager not connected")
            return False
        
        print("🏆 Initializing title system...")
        
        # Find title-related memory addresses
        if not self._find_title_addresses():
            print("❌ Failed to find title addresses")
            return False
        
        print("✅ Title system initialized")
        return True
    
    def _find_title_addresses(self) -> bool:
        """Find memory addresses related to titles"""
        found_addresses = 0
        
        for pattern_name, pattern_info in self.title_patterns.items():
            try:
                address = self.memory._pattern_scan(
                    pattern_info['pattern'],
                    pattern_info['mask']
                )
                
                if address:
                    final_address = address + pattern_info['offset']
                    self.title_addresses[pattern_name] = final_address
                    found_addresses += 1
                    print(f"✅ Found {pattern_name}: 0x{final_address:X}")
                else:
                    print(f"❌ Pattern not found: {pattern_name}")
                    
            except Exception as e:
                print(f"❌ Error finding {pattern_name}: {e}")
        
        return found_addresses > 0
    
    def _load_default_titles(self):
        """Load default Rocket League titles"""
        default_titles = [
            Title(1, "Rookie", "Rookie", False, "Common", "Level 1"),
            Title(2, "Semi-Pro", "Semi-Pro", False, "Common", "Level 10"),
            Title(3, "Pro", "Pro", False, "Common", "Level 20"),
            Title(4, "Veteran", "Veteran", False, "Common", "Level 30"),
            Title(5, "Expert", "Expert", False, "Common", "Level 40"),
            Title(6, "Master", "Master", False, "Common", "Level 50"),
            Title(7, "Legend", "Legend", False, "Common", "Level 60"),
            Title(8, "Rocketeer", "Rocketeer", False, "Common", "Level 75"),
            
            # Competitive titles
            Title(100, "Grand Champion", "Grand Champion", False, "Legendary", "Competitive"),
            Title(101, "Supersonic Legend", "Supersonic Legend", False, "Legendary", "Competitive"),
            
            # Season titles
            Title(200, "Season 1 Grand Champion", "Season 1 Grand Champion", False, "Limited", "Season 1"),
            Title(201, "Season 2 Grand Champion", "Season 2 Grand Champion", False, "Limited", "Season 2"),
            Title(202, "Season 3 Grand Champion", "Season 3 Grand Champion", False, "Limited", "Season 3"),
            
            # Special titles
            Title(300, "RLCS Champion", "RLCS Champion", False, "Legendary", "RLCS"),
            Title(301, "RLCS Season X Champion", "RLCS Season X Champion", False, "Legendary", "RLCS"),
            Title(302, "World Champion", "World Champion", False, "Legendary", "RLCS"),
            
            # Developer/Special titles
            Title(999, "Psyonix", "Psyonix", False, "Developer", "Developer"),
            Title(998, "Epic Games", "Epic Games", False, "Developer", "Developer"),
            Title(997, "Moderator", "Moderator", False, "Special", "Staff"),
            
            # Fun titles
            Title(500, "Ball Chaser", "Ball Chaser", False, "Rare", "Achievement"),
            Title(501, "Demolition Expert", "Demolition Expert", False, "Rare", "Achievement"),
            Title(502, "Aerial Ace", "Aerial Ace", False, "Rare", "Achievement"),
            Title(503, "Goalkeeper", "Goalkeeper", False, "Rare", "Achievement"),
            Title(504, "MVP", "MVP", False, "Epic", "Achievement"),
        ]
        
        self.available_titles = default_titles
    
    def _load_custom_titles(self):
        """Load custom titles from config"""
        try:
            with open('custom_titles.json', 'r') as f:
                data = json.load(f)
                custom_titles = data.get('custom_titles', [])
                
                for i, title_text in enumerate(custom_titles):
                    custom_title = Title(
                        id=9000 + i,
                        name=f"Custom_{i}",
                        display_text=title_text,
                        is_custom=True,
                        rarity="Custom",
                        unlock_method="Custom"
                    )
                    self.available_titles.append(custom_title)
                    
        except FileNotFoundError:
            # Create default custom titles file
            self._save_custom_titles()
        except Exception as e:
            print(f"Error loading custom titles: {e}")
    
    def _save_custom_titles(self):
        """Save custom titles to config"""
        try:
            custom_title_texts = [
                title.display_text for title in self.available_titles 
                if title.is_custom
            ]
            
            data = {'custom_titles': custom_title_texts}
            
            with open('custom_titles.json', 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving custom titles: {e}")
    
    def get_available_titles(self) -> List[Title]:
        """Get list of all available titles"""
        return self.available_titles
    
    def get_current_title(self) -> Optional[Title]:
        """Get currently selected title"""
        if 0 <= self.current_title_index < len(self.available_titles):
            return self.available_titles[self.current_title_index]
        return None
    
    def set_title(self, index: int) -> bool:
        """Set title by index"""
        if 0 <= index < len(self.available_titles):
            self.current_title_index = index
            return self._apply_title()
        return False
    
    def set_title_by_name(self, title_name: str) -> bool:
        """Set title by name"""
        for i, title in enumerate(self.available_titles):
            if title.display_text.lower() == title_name.lower():
                self.current_title_index = i
                return self._apply_title()
        return False
    
    def cycle_title(self) -> bool:
        """Cycle to next title"""
        self.current_title_index = (self.current_title_index + 1) % len(self.available_titles)
        return self._apply_title()
    
    def add_custom_title(self, title_text: str) -> bool:
        """Add a new custom title"""
        if not title_text.strip():
            return False
        
        # Check if title already exists
        for title in self.available_titles:
            if title.display_text.lower() == title_text.lower():
                print(f"Title '{title_text}' already exists")
                return False
        
        # Create new custom title
        new_id = max([t.id for t in self.available_titles if t.is_custom], default=9000) + 1
        custom_title = Title(
            id=new_id,
            name=f"Custom_{new_id}",
            display_text=title_text,
            is_custom=True,
            rarity="Custom",
            unlock_method="Custom"
        )
        
        self.available_titles.append(custom_title)
        self._save_custom_titles()
        
        print(f"✅ Added custom title: '{title_text}'")
        return True
    
    def remove_custom_title(self, title_text: str) -> bool:
        """Remove a custom title"""
        for i, title in enumerate(self.available_titles):
            if title.is_custom and title.display_text.lower() == title_text.lower():
                self.available_titles.pop(i)
                self._save_custom_titles()
                
                # Adjust current index if necessary
                if self.current_title_index >= len(self.available_titles):
                    self.current_title_index = 0
                
                print(f"✅ Removed custom title: '{title_text}'")
                return True
        
        print(f"❌ Custom title '{title_text}' not found")
        return False
    
    def _apply_title(self) -> bool:
        """Apply the currently selected title"""
        current_title = self.get_current_title()
        if not current_title:
            return False
        
        try:
            # Method 1: Direct memory modification
            success = self._write_title_to_memory(current_title.display_text)
            
            # Method 2: Hook title display function
            if not success:
                success = self._hook_title_display(current_title.display_text)
            
            # Method 3: Modify network packets
            if not success:
                success = self._modify_network_title(current_title.display_text)
            
            if success:
                print(f"✅ Applied title: '{current_title.display_text}'")
                return True
            else:
                print(f"❌ Failed to apply title: '{current_title.display_text}'")
                return False
                
        except Exception as e:
            print(f"❌ Error applying title: {e}")
            return False
    
    def _write_title_to_memory(self, title_text: str) -> bool:
        """Write title directly to memory"""
        try:
            title_address = self.title_addresses.get('local_player_title')
            if not title_address:
                return False
            
            # Write title string to memory
            return self.memory.write_string(title_address, title_text)
            
        except Exception as e:
            print(f"Error writing title to memory: {e}")
            return False
    
    def _hook_title_display(self, title_text: str) -> bool:
        """Hook title display function"""
        try:
            # This would involve hooking the function that displays titles
            # and replacing the return value with our custom title
            # For now, this is a placeholder
            
            display_func_addr = self.title_addresses.get('title_display_func')
            if not display_func_addr:
                return False
            
            # In a real implementation, you would:
            # 1. Create a code cave or trampoline
            # 2. Hook the function to redirect to your code
            # 3. Return your custom title string
            
            print(f"Would hook title display function at 0x{display_func_addr:X}")
            return True
            
        except Exception as e:
            print(f"Error hooking title display: {e}")
            return False
    
    def _modify_network_title(self, title_text: str) -> bool:
        """Modify network packets containing title data"""
        try:
            # This would involve intercepting network packets
            # and modifying the title data before it's sent to the server
            
            network_addr = self.title_addresses.get('network_title_data')
            if not network_addr:
                return False
            
            # In a real implementation, you would:
            # 1. Find the network packet structure
            # 2. Locate the title field in the packet
            # 3. Replace it with your custom title
            
            print(f"Would modify network title data at 0x{network_addr:X}")
            return True
            
        except Exception as e:
            print(f"Error modifying network title: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get current title system status"""
        current_title = self.get_current_title()
        
        return {
            'current_title': current_title.display_text if current_title else "None",
            'current_index': self.current_title_index,
            'total_titles': len(self.available_titles),
            'custom_titles': len([t for t in self.available_titles if t.is_custom]),
            'addresses_found': len([addr for addr in self.title_addresses.values() if addr]),
            'memory_connected': self.memory.is_connected
        }
    
    def search_titles(self, query: str) -> List[Title]:
        """Search for titles containing the query"""
        query_lower = query.lower()
        return [
            title for title in self.available_titles
            if query_lower in title.display_text.lower() or query_lower in title.name.lower()
        ]
    
    def get_titles_by_rarity(self, rarity: str) -> List[Title]:
        """Get titles by rarity"""
        return [title for title in self.available_titles if title.rarity.lower() == rarity.lower()]
    
    def force_refresh(self) -> bool:
        """Force refresh of title addresses"""
        print("🔄 Refreshing title addresses...")
        self.title_addresses = {key: None for key in self.title_addresses.keys()}
        return self._find_title_addresses()
