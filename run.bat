@echo off
title Rocket League Mod - Python Edition

echo ========================================
echo   Rocket League Mod - Python Edition
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo 📦 Checking dependencies...
python -c "import pymem, psutil, customtkinter, keyboard" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Dependencies not found. Running installation...
    echo.
    python install.py
    echo.
    echo Dependencies installed. Restarting...
    echo.
)

REM Check if Rocket League is running
echo 🔍 Checking for Rocket League...
tasklist /FI "IMAGENAME eq RocketLeague.exe" 2>NUL | find /I /N "RocketLeague.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Rocket League is running
) else (
    echo ⚠️ Rocket League is not running
    echo Please start Rocket League first for best results
)

echo.
echo 🚀 Starting Rocket League Mod...
echo.
echo ⌨️ Hotkeys:
echo   F1 - Toggle car invisibility
echo   F2 - Cycle titles  
echo   F3 - Toggle GUI
echo   F4 - Emergency restore
echo.
echo 💡 Tip: Run as Administrator for better compatibility
echo.

REM Start the mod
python rocket_league_mod.py

echo.
echo 👋 Mod has exited
pause
