#pragma once

// Windows headers
#include <windows.h>
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <thread>
#include <mutex>
#include <map>
#include <fstream>

// DirectX headers
#include <d3d11.h>
#include <d3dcompiler.h>
#include <DirectXMath.h>

// ImGui headers
#include "imgui/imgui.h"
#include "imgui/imgui_impl_win32.h"
#include "imgui/imgui_impl_dx11.h"

// Memory manipulation
#include <TlHelp32.h>
#include <Psapi.h>

// JSON for config
#include "json/json.hpp"
using json = nlohmann::json;

// Common typedefs
typedef unsigned char byte;
typedef unsigned int uint;
typedef unsigned long ulong;
typedef unsigned long long uint64;

// Useful macros
#define SAFE_DELETE(p) { if(p) { delete (p); (p) = nullptr; } }
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }

// Memory utilities
namespace Memory {
    bool WriteMemory(void* address, const void* data, size_t size);
    bool ReadMemory(void* address, void* buffer, size_t size);
    void* FindPattern(const char* pattern, const char* mask);
    DWORD GetProcessId(const wchar_t* processName);
    uintptr_t GetModuleBaseAddress(DWORD processId, const wchar_t* moduleName);
}
