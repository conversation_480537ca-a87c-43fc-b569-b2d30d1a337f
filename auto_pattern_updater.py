#!/usr/bin/env python3
"""
Auto Pattern Updater
Automatically finds memory patterns and updates mod files
"""

import pymem
import psutil
import struct
import re
import os
import shutil
from typing import Dict, List, Optional, Tuple
import time


class AutoPatternUpdater:
    """Automatically finds and updates memory patterns"""
    
    def __init__(self):
        self.process: Optional[pymem.Pymem] = None
        self.base_address: Optional[int] = None
        self.module_size: Optional[int] = None
        self.found_patterns: Dict[str, int] = {}
        
    def connect_to_game(self) -> bool:
        """Connect to Rocket League"""
        print("🔍 Searching for Rocket League...")
        
        try:
            # Find Rocket League process
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name']
                if any(name in proc_name.lower() for name in ['rocket', 'rl.exe']):
                    break
            else:
                print("❌ Rocket League not found!")
                print("   Please start Rocket League and try again.")
                return False
            
            # Connect to process
            self.process = pymem.Pymem(proc_name)
            
            # Get module info
            modules = list(self.process.list_modules())
            main_module = modules[0]
            self.base_address = main_module.lpBaseOfDll
            self.module_size = main_module.SizeOfImage
            
            print(f"✅ Connected to {proc_name}")
            print(f"📍 Base: 0x{self.base_address:X}")
            print(f"📏 Size: 0x{self.module_size:X}")
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def find_all_patterns(self) -> bool:
        """Find all required patterns"""
        print("\n🔍 Scanning for memory patterns...")
        
        patterns_to_find = {
            # Car invisibility patterns
            'car_visibility_flag': [
                (b'\x88\x44\x24\x20\x48\x8B\xCF', 'xxxxxxx'),  # mov [rsp+20], al; mov rcx, rdi
                (b'\x88\x47\x00\x48\x8B', 'xx?xx'),            # mov [rdi+?], al; mov
                (b'\x80\x7F\x00\x00\x74', 'xx??x'),            # cmp byte ptr [rdi+?], 0; jz
            ],
            'render_state': [
                (b'\x48\x8B\x40\x00\x48\x85\xC0\x74', 'xxx?xxxx'),  # mov rax, [rax+?]; test rax, rax; jz
                (b'\x48\x8B\x48\x00\x48\x85\xC9\x75', 'xxx?xxxx'),  # mov rcx, [rax+?]; test rcx, rcx; jnz
            ],
            'mesh_visibility': [
                (b'\x40\x88\x77\x00\x48\x8B', 'xxx?xx'),       # mov [rdi+?], sil; mov
                (b'\x88\x46\x00\x48\x8B\xCE', 'xx?xxx'),       # mov [rsi+?], al; mov rcx, rsi
            ],
            
            # Title system patterns
            'title_string_ptr': [
                (b'\x48\x8D\x15\x00\x00\x00\x00\x48\x8B\xCF', 'xxx????xxx'),  # lea rdx, [string]; mov rcx, rdi
                (b'\x48\x8B\x05\x00\x00\x00\x00\x48\x85\xC0', 'xxx????xxx'),  # mov rax, [ptr]; test rax, rax
            ],
            'player_title_offset': [
                (b'\x48\x8B\x80\x00\x00\x00\x00\x48\x85\xC0', 'xxx????xxx'),  # mov rax, [rax+offset]; test rax, rax
                (b'\x48\x8B\x88\x00\x00\x00\x00\x48\x85\xC9', 'xxx????xxx'),  # mov rcx, [rax+offset]; test rcx, rcx
            ],
            'title_display_func': [
                (b'\xE8\x00\x00\x00\x00\x48\x8B\x4C\x24', 'x????xxxx'),      # call func; mov rcx, [rsp+?]
                (b'\xFF\x15\x00\x00\x00\x00\x48\x8B', 'xx????xx'),           # call [ptr]; mov
            ],
            
            # Player object patterns
            'local_player_ptr': [
                (b'\x48\x8B\x0D\x00\x00\x00\x00\x48\x85\xC9', 'xxx????xxx'),  # mov rcx, [local_player]; test rcx, rcx
                (b'\x48\x8B\x15\x00\x00\x00\x00\x48\x85\xD2', 'xxx????xxx'),  # mov rdx, [local_player]; test rdx, rdx
            ],
            'car_object_offset': [
                (b'\x48\x8B\x81\x00\x00\x00\x00\x48\x85\xC0', 'xxx????xxx'),  # mov rax, [rcx+car_offset]; test rax, rax
                (b'\x48\x8B\x89\x00\x00\x00\x00\x48\x85\xC9', 'xxx????xxx'),  # mov rcx, [rcx+car_offset]; test rcx, rcx
            ]
        }
        
        found_count = 0
        total_patterns = len(patterns_to_find)
        
        for pattern_name, pattern_list in patterns_to_find.items():
            print(f"  🔍 Searching for {pattern_name}...")
            
            for pattern, mask in pattern_list:
                addresses = self._scan_pattern(pattern, mask)
                if addresses:
                    # Take the first valid address
                    self.found_patterns[pattern_name] = addresses[0]
                    print(f"    ✅ Found at 0x{addresses[0]:X}")
                    found_count += 1
                    break
            else:
                print(f"    ❌ Not found")
        
        print(f"\n📊 Results: {found_count}/{total_patterns} patterns found")
        
        # Require at least 60% of patterns to be found
        success_threshold = 0.6
        if found_count / total_patterns >= success_threshold:
            print(f"✅ Sufficient patterns found ({found_count}/{total_patterns})")
            return True
        else:
            print(f"❌ Insufficient patterns found ({found_count}/{total_patterns})")
            print(f"   Need at least {int(total_patterns * success_threshold)} patterns")
            return False
    
    def _scan_pattern(self, pattern: bytes, mask: str) -> List[int]:
        """Scan for pattern with mask"""
        addresses = []
        chunk_size = 0x10000  # 64KB chunks
        
        try:
            for offset in range(0, self.module_size, chunk_size):
                if offset + len(pattern) > self.module_size:
                    break
                
                try:
                    chunk = self.process.read_bytes(
                        self.base_address + offset,
                        min(chunk_size, self.module_size - offset)
                    )
                    
                    for i in range(len(chunk) - len(pattern)):
                        match = True
                        for j in range(len(pattern)):
                            if mask[j] != '?' and chunk[i + j] != pattern[j]:
                                match = False
                                break
                        
                        if match:
                            addr = self.base_address + offset + i
                            # Validate address is reasonable
                            if self._validate_address(addr):
                                addresses.append(addr)
                            
                            # Limit results to avoid spam
                            if len(addresses) >= 10:
                                return addresses
                                
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"    Scan error: {e}")
        
        return addresses
    
    def _validate_address(self, address: int) -> bool:
        """Validate if address looks reasonable"""
        try:
            # Check if address is within module bounds
            if not (self.base_address <= address < self.base_address + self.module_size):
                return False
            
            # Try to read a small amount to verify accessibility
            self.process.read_bytes(address, 4)
            return True
            
        except Exception:
            return False
    
    def update_mod_files(self) -> bool:
        """Update mod files with found patterns"""
        print("\n🔧 Updating mod files...")
        
        try:
            # Backup original files
            self._backup_files()
            
            # Update car invisibility file
            if self._update_car_invisibility_file():
                print("  ✅ Updated car_invisibility.py")
            else:
                print("  ❌ Failed to update car_invisibility.py")
            
            # Update title system file
            if self._update_title_system_file():
                print("  ✅ Updated title_system.py")
            else:
                print("  ❌ Failed to update title_system.py")
            
            # Update memory manager file
            if self._update_memory_manager_file():
                print("  ✅ Updated memory_manager.py")
            else:
                print("  ❌ Failed to update memory_manager.py")
            
            print("✅ All mod files updated successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error updating files: {e}")
            return False
    
    def _backup_files(self):
        """Backup original files"""
        files_to_backup = [
            'car_invisibility.py',
            'title_system.py', 
            'memory_manager.py'
        ]
        
        backup_dir = 'backup_original'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        for filename in files_to_backup:
            if os.path.exists(filename):
                backup_path = os.path.join(backup_dir, filename)
                shutil.copy2(filename, backup_path)
        
        print(f"  💾 Original files backed up to {backup_dir}/")
    
    def _update_car_invisibility_file(self) -> bool:
        """Update car invisibility patterns"""
        try:
            with open('car_invisibility.py', 'r') as f:
                content = f.read()
            
            # Generate new patterns
            new_patterns = self._generate_car_patterns()
            
            # Replace the visibility_patterns section
            pattern = r"self\.visibility_patterns = \{[^}]*\}"
            replacement = f"self.visibility_patterns = {new_patterns}"
            
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # Also update the make_invisible and make_visible methods to use real patterns
            content = self._update_car_methods(content)
            
            with open('car_invisibility.py', 'w') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"    Error updating car file: {e}")
            return False
    
    def _generate_car_patterns(self) -> str:
        """Generate car invisibility patterns"""
        patterns = "{\n"
        
        if 'car_visibility_flag' in self.found_patterns:
            addr = self.found_patterns['car_visibility_flag']
            patterns += f"            'car_visibility_flag': {{\n"
            patterns += f"                'pattern': b'\\x88\\x44\\x24\\x20',\n"
            patterns += f"                'mask': 'xxxx',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 0\n"
            patterns += f"            }},\n"
        
        if 'render_state' in self.found_patterns:
            addr = self.found_patterns['render_state']
            patterns += f"            'render_state': {{\n"
            patterns += f"                'pattern': b'\\x48\\x8B\\x40\\x00',\n"
            patterns += f"                'mask': 'xxx?',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 2\n"
            patterns += f"            }},\n"
        
        if 'mesh_visibility' in self.found_patterns:
            addr = self.found_patterns['mesh_visibility']
            patterns += f"            'mesh_visibility': {{\n"
            patterns += f"                'pattern': b'\\x40\\x88\\x77\\x00',\n"
            patterns += f"                'mask': 'xxx?',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 3\n"
            patterns += f"            }}\n"
        
        patterns += "        }"
        return patterns
    
    def _update_car_methods(self, content: str) -> str:
        """Update car methods to use real memory manipulation"""
        # Replace demo mode make_invisible method
        old_invisible = r'def make_invisible\(self\) -> bool:.*?return True'
        new_invisible = '''def make_invisible(self) -> bool:
        """Make the car invisible"""
        if not self.memory.is_connected:
            print("❌ Not connected to game")
            return False
        
        if self.is_invisible:
            print("ℹ️ Car is already invisible")
            return True
        
        print("👻 Making car invisible...")
        
        try:
            success = False
            
            # Method 1: Set visibility flags
            if 'car_visibility_flag' in self.visibility_patterns:
                addr = self.visibility_patterns['car_visibility_flag']['address']
                if self.memory.write_memory(addr, b'\\x00'):  # Set to invisible
                    success = True
                    print("  ✅ Set visibility flag")
            
            # Method 2: Modify render state
            if 'render_state' in self.visibility_patterns:
                addr = self.visibility_patterns['render_state']['address']
                if self.memory.write_int(addr, 0):  # Disable rendering
                    success = True
                    print("  ✅ Modified render state")
            
            if success:
                self.is_invisible = True
                print("✅ Car is now invisible!")
                return True
            else:
                print("❌ Failed to make car invisible")
                return False
                
        except Exception as e:
            print(f"❌ Error making car invisible: {e}")
            return False'''
        
        content = re.sub(old_invisible, new_invisible, content, flags=re.DOTALL)
        
        # Replace demo mode make_visible method
        old_visible = r'def make_visible\(self\) -> bool:.*?return True'
        new_visible = '''def make_visible(self) -> bool:
        """Make the car visible again"""
        if not self.memory.is_connected:
            print("❌ Not connected to game")
            return False
        
        if not self.is_invisible:
            print("ℹ️ Car is already visible")
            return True
        
        print("👁️ Making car visible...")
        
        try:
            success = False
            
            # Method 1: Restore visibility flags
            if 'car_visibility_flag' in self.visibility_patterns:
                addr = self.visibility_patterns['car_visibility_flag']['address']
                if self.memory.write_memory(addr, b'\\x01'):  # Set to visible
                    success = True
                    print("  ✅ Restored visibility flag")
            
            # Method 2: Restore render state
            if 'render_state' in self.visibility_patterns:
                addr = self.visibility_patterns['render_state']['address']
                if self.memory.write_int(addr, 1):  # Enable rendering
                    success = True
                    print("  ✅ Restored render state")
            
            if success:
                self.is_invisible = False
                print("✅ Car is now visible!")
                return True
            else:
                print("❌ Failed to make car visible")
                return False
                
        except Exception as e:
            print(f"❌ Error making car visible: {e}")
            return False'''
        
        content = re.sub(old_visible, new_visible, content, flags=re.DOTALL)
        
        return content

    def _update_title_system_file(self) -> bool:
        """Update title system patterns"""
        try:
            with open('title_system.py', 'r') as f:
                content = f.read()

            # Generate new patterns
            new_patterns = self._generate_title_patterns()

            # Replace the title_patterns section
            pattern = r"self\.title_patterns = \{[^}]*\}"
            replacement = f"self.title_patterns = {new_patterns}"

            content = re.sub(pattern, replacement, content, flags=re.DOTALL)

            # Update the _apply_title method
            content = self._update_title_methods(content)

            with open('title_system.py', 'w') as f:
                f.write(content)

            return True

        except Exception as e:
            print(f"    Error updating title file: {e}")
            return False

    def _generate_title_patterns(self) -> str:
        """Generate title system patterns"""
        patterns = "{\n"

        if 'title_string_ptr' in self.found_patterns:
            addr = self.found_patterns['title_string_ptr']
            patterns += f"            'title_string': {{\n"
            patterns += f"                'pattern': b'\\x48\\x8D\\x15\\x00\\x00\\x00\\x00',\n"
            patterns += f"                'mask': 'xxx????',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 3\n"
            patterns += f"            }},\n"

        if 'player_title_offset' in self.found_patterns:
            addr = self.found_patterns['player_title_offset']
            patterns += f"            'player_title_offset': {{\n"
            patterns += f"                'pattern': b'\\x48\\x8B\\x80\\x00\\x00\\x00\\x00',\n"
            patterns += f"                'mask': 'xxx????',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 3\n"
            patterns += f"            }},\n"

        if 'title_display_func' in self.found_patterns:
            addr = self.found_patterns['title_display_func']
            patterns += f"            'title_display_func': {{\n"
            patterns += f"                'pattern': b'\\xE8\\x00\\x00\\x00\\x00',\n"
            patterns += f"                'mask': 'x????',\n"
            patterns += f"                'address': 0x{addr:X},\n"
            patterns += f"                'offset': 1\n"
            patterns += f"            }}\n"

        patterns += "        }"
        return patterns

    def _update_title_methods(self, content: str) -> str:
        """Update title methods to use real memory manipulation"""
        # Replace demo mode _apply_title method
        old_apply = r'def _apply_title\(self\) -> bool:.*?return True'
        new_apply = '''def _apply_title(self) -> bool:
        """Apply the currently selected title"""
        current_title = self.get_current_title()
        if not current_title:
            return False

        try:
            success = False

            # Method 1: Direct memory modification
            if 'player_title_offset' in self.title_patterns:
                addr = self.title_patterns['player_title_offset']['address']
                title_bytes = current_title.display_text.encode('utf-8') + b'\\x00'
                if self.memory.write_memory(addr, title_bytes):
                    success = True
                    print(f"  ✅ Wrote title to memory")

            # Method 2: String pointer modification
            if 'title_string' in self.title_patterns:
                addr = self.title_patterns['title_string']['address']
                # Calculate RIP-relative address
                rel_offset = struct.unpack('<i', self.memory.read_memory(addr, 4))[0]
                string_addr = addr + 4 + rel_offset
                title_bytes = current_title.display_text.encode('utf-8') + b'\\x00'
                if self.memory.write_memory(string_addr, title_bytes):
                    success = True
                    print(f"  ✅ Updated title string")

            if success:
                print(f"✅ Applied title: '{current_title.display_text}'")
                return True
            else:
                print(f"❌ Failed to apply title: '{current_title.display_text}'")
                return False

        except Exception as e:
            print(f"❌ Error applying title: {e}")
            return False'''

        content = re.sub(old_apply, new_apply, content, flags=re.DOTALL)
        return content

    def _update_memory_manager_file(self) -> bool:
        """Update memory manager with found addresses"""
        try:
            with open('memory_manager.py', 'r') as f:
                content = f.read()

            # Add found addresses to the patterns dictionary
            new_patterns = self._generate_memory_patterns()

            # Replace the patterns section
            pattern = r"self\.patterns = \{[^}]*\}"
            replacement = f"self.patterns = {new_patterns}"

            content = re.sub(pattern, replacement, content, flags=re.DOTALL)

            with open('memory_manager.py', 'w') as f:
                f.write(content)

            return True

        except Exception as e:
            print(f"    Error updating memory manager: {e}")
            return False

    def _generate_memory_patterns(self) -> str:
        """Generate memory manager patterns"""
        patterns = "{\n"

        for pattern_name, address in self.found_patterns.items():
            patterns += f"            '{pattern_name}': MemoryPattern(\n"
            patterns += f"                pattern=b'\\x90\\x90\\x90\\x90',  # Placeholder\n"
            patterns += f"                mask='xxxx',\n"
            patterns += f"                offset=0,\n"
            patterns += f"                name='{pattern_name}'\n"
            patterns += f"            ),\n"

        patterns += "        }"
        return patterns

    def run(self) -> bool:
        """Main execution function"""
        print("🚀 Auto Pattern Updater for Rocket League Mod")
        print("=" * 50)

        # Step 1: Connect to game
        if not self.connect_to_game():
            print("\n❌ FAILED: Could not connect to Rocket League")
            print("   Please start Rocket League and try again.")
            return False

        # Step 2: Find patterns
        if not self.find_all_patterns():
            print("\n❌ FAILED: Insufficient patterns found")
            print("   Your game version may not be supported yet.")
            print("   Try updating Rocket League or wait for pattern updates.")
            return False

        # Step 3: Update mod files
        if not self.update_mod_files():
            print("\n❌ FAILED: Could not update mod files")
            return False

        # Step 4: Success!
        print("\n" + "=" * 50)
        print("✅ SUCCESS! Your mod has been updated!")
        print("=" * 50)
        print(f"📊 Found and applied {len(self.found_patterns)} patterns:")

        for pattern_name, address in self.found_patterns.items():
            print(f"  ✅ {pattern_name}: 0x{address:X}")

        print("\n🎮 Your mod is now fully functional!")
        print("   Run: python rocket_league_mod.py")
        print("\n⌨️ Hotkeys:")
        print("   F1: Toggle car invisibility (REAL)")
        print("   F2: Cycle titles (REAL)")
        print("   F3: Toggle GUI")
        print("   F4: Emergency restore")

        print("\n💾 Original files backed up to backup_original/")
        print("   You can restore them if needed.")

        return True


def main():
    """Main function"""
    updater = AutoPatternUpdater()

    try:
        success = updater.run()

        if success:
            print("\n🎉 Ready to use your fully functional mod!")
            input("Press Enter to exit...")
        else:
            print("\n💡 Tips:")
            print("   - Make sure Rocket League is running")
            print("   - Try restarting the game")
            print("   - Check if you have the latest game version")
            input("Press Enter to exit...")

    except KeyboardInterrupt:
        print("\n⏹️ Cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
