#!/usr/bin/env python3
"""
Installation script for Rocket League Mod
Handles dependency installation and setup
"""

import sys
import subprocess
import os
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def check_admin_rights():
    """Check if running with admin rights (needed for global hotkeys)"""
    try:
        if platform.system() == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                print("⚠️ Warning: Not running as administrator")
                print("   Global hotkeys may not work properly")
                print("   Consider running as administrator for full functionality")
            else:
                print("✅ Running with administrator privileges")
            return is_admin
        else:
            # For non-Windows systems
            return os.geteuid() == 0
    except Exception:
        return False


def create_config_files():
    """Create default configuration files"""
    print("📄 Creating configuration files...")
    
    try:
        # Create custom titles file if it doesn't exist
        custom_titles_file = "custom_titles.json"
        if not os.path.exists(custom_titles_file):
            import json
            default_custom_titles = {
                "custom_titles": [
                    "Custom Champion",
                    "Mod User",
                    "Python Master"
                ]
            }
            
            with open(custom_titles_file, 'w') as f:
                json.dump(default_custom_titles, f, indent=2)
            
            print(f"✅ Created {custom_titles_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating config files: {e}")
        return False


def test_dependencies():
    """Test if all dependencies are working"""
    print("🧪 Testing dependencies...")
    
    dependencies = [
        ("pymem", "Memory manipulation"),
        ("psutil", "Process utilities"),
        ("customtkinter", "Modern GUI"),
        ("keyboard", "Global hotkeys"),
        ("pynput", "Input handling")
    ]
    
    failed = []
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description}")
            failed.append(module)
    
    if failed:
        print(f"\n❌ Failed to import: {', '.join(failed)}")
        print("Try running: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are working!")
    return True


def create_shortcuts():
    """Create desktop shortcuts"""
    print("🔗 Creating shortcuts...")
    
    try:
        if platform.system() == "Windows":
            # Create batch file for easy launching
            batch_content = f"""@echo off
cd /d "{os.getcwd()}"
python rocket_league_mod.py
pause
"""
            
            with open("Launch_RL_Mod.bat", 'w') as f:
                f.write(batch_content)
            
            print("✅ Created Launch_RL_Mod.bat")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating shortcuts: {e}")
        return False


def setup_firewall_exception():
    """Setup Windows Firewall exception if needed"""
    if platform.system() != "Windows":
        return True
    
    print("🔥 Checking Windows Firewall...")
    
    try:
        # This is optional - some users might need firewall exceptions
        print("ℹ️ If you encounter connection issues, you may need to:")
        print("   1. Add Python to Windows Firewall exceptions")
        print("   2. Run the mod as administrator")
        return True
        
    except Exception as e:
        print(f"⚠️ Firewall check warning: {e}")
        return True


def main():
    """Main installation function"""
    print("🚀 Rocket League Mod - Installation Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Check admin rights
    check_admin_rights()
    
    # Install dependencies
    if not install_dependencies():
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Test dependencies
    if not test_dependencies():
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Create config files
    create_config_files()
    
    # Create shortcuts
    create_shortcuts()
    
    # Setup firewall
    setup_firewall_exception()
    
    print("\n" + "=" * 50)
    print("✅ Installation completed successfully!")
    print("=" * 50)
    print("\n📋 Next steps:")
    print("1. Start Rocket League")
    print("2. Run: python rocket_league_mod.py")
    print("3. Or double-click: Launch_RL_Mod.bat")
    print("\n⌨️ Default hotkeys:")
    print("- F1: Toggle car invisibility")
    print("- F2: Cycle titles")
    print("- F3: Toggle GUI")
    print("- F4: Emergency restore")
    print("\n⚠️ Important:")
    print("- This mod is for educational purposes only")
    print("- Use at your own risk")
    print("- May require game-specific memory patterns")
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
